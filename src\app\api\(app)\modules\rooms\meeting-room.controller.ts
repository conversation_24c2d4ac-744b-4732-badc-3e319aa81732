import { Elysia } from 'elysia';
import { MeetingRoomService } from './meeting-room.service';
import {
  createMeetingRoom,
  updateMeetingRoom,
  getMeetingRoomQuery,
  meetingRoomParams,
  checkAvailabilityQuery
} from './meeting-rooms.validators';
import { authMiddleware } from '../../middlewares/auth.middleware';

// export const meetingRoomController = new Elysia({ prefix: '/meeting-rooms' })
export const meetingRoomController = new Elysia({
  prefix: '/meeting-rooms',
  detail: {
    tags: ['Meeting Rooms'],
    description: 'Endpoints for managing meeting rooms'
  }
})
  .use(authMiddleware)

  // Get all meeting rooms
  .get('/', async ({ query }) => {
    console.log('query', query);
    try {
      const result = await MeetingRoomService.getAllMeetingRooms(query);
      return {
        success: true,
        data: result.data,
        pagination: result.pagination
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }, {
    query: getMeetingRoomQuery
  })

  // Create new meeting room
  .post('/', async ({ body }) => {
    try {
      const meetingRoom = await MeetingRoomService.createMeetingRoom(body);
      return {
        success: true,
        data: meetingRoom
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }, {
    body: createMeetingRoom
  })

  // Get meeting room by ID
  .get('/:id', async ({ params }) => {
    try {
      const meetingRoom = await MeetingRoomService.getMeetingRoomById(params.id);
      return {
        success: true,
        data: meetingRoom
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }, {
    params: meetingRoomParams
  })

  // Update meeting room
  .put('/:id', async ({ params, body }) => {
    try {
      const meetingRoom = await MeetingRoomService.updateMeetingRoom(params.id, body);
      return {
        success: true,
        data: meetingRoom
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }, {
    params: meetingRoomParams,
    body: updateMeetingRoom
  })

  // Delete meeting room
  .delete('/:id', async ({ params }) => {
    try {
      await MeetingRoomService.deleteMeetingRoom(params.id);
      return {
        success: true,
        message: 'Meeting room deleted successfully'
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }, {
    params: meetingRoomParams
  })

  // Check meeting room availability
  .get('/:id/availability', async ({ params, query }) => {
    try {
      const availability = await MeetingRoomService.checkAvailability(params.id, query);
      return {
        success: true,
        data: availability
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }, {
    params: meetingRoomParams,
    query: checkAvailabilityQuery
  })

  // Get meeting room bookings
  .get('/:id/bookings', async ({ params, query }) => {
    try {
      const result = await MeetingRoomService.getMeetingRoomBookings(params.id, query);
      return {
        success: true,
        data: result.data,
        pagination: result.pagination
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }, {
    params: meetingRoomParams
  });

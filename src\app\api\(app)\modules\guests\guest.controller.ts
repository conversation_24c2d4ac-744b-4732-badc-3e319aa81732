import { Elysia } from 'elysia';
import { GuestService } from './guest.service';
import {
  createGuest,
  updateGuest,
  getGuestsQuery,
  guestParams,
  // checkInOutGuest,
  bulkGuestOperation,
  // sendInvitation,
  getDailyGuestsQuery
} from './guests.validators';
import { authMiddleware } from '../../middlewares/auth.middleware';

// export const guestController = new Elysia({ prefix: '/guests' })
export const guestController = new Elysia({
  prefix: '/guests',
  detail: {
    tags: ['Guests'],
    description: 'Endpoints for managing guests'
  }
})
  .use(authMiddleware)

  // Get all guests
  .get('/', async ({ query }) => {
    try {
      const result = await GuestService.getAllGuests(query);
      return {
        success: true,
        data: result.data,
        pagination: result.pagination
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }, {
    query: getGuestsQuery
  })

  // Create new guest (requires hostId from auth context)
  .post('/', async ({ body, headers }) => {
    try {
      const hostId = headers['x-user-id'] || 'temp-user-id';
      const guest = await GuestService.createGuest(body, hostId);
      return {
        success: true,
        data: guest
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }, {
    body: createGuest
  })

  // Get guest by ID
  .get('/:id', async ({ params, headers }) => {
    try {
      const hostId = headers['x-user-id'];
      const guest = await GuestService.getGuestById(params.id, hostId);
      return {
        success: true,
        data: guest
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }, {
    params: guestParams
  })

  // Update guest
  .put('/:id', async ({ params, body, headers }) => {
    try {
      const hostId = headers['x-user-id'];
      const guest = await GuestService.updateGuest(params.id, body, hostId);
      return {
        success: true,
        data: guest
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }, {
    params: guestParams,
    body: updateGuest
  })

  // Delete guest
  .delete('/:id', async ({ params, headers }) => {
    try {
      const hostId = headers['x-user-id'];
      const result = await GuestService.deleteGuest(params.id, hostId);
      return {
        success: true,
        message: 'Guest deleted successfully',
        data: result
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }, {
    params: guestParams
  })

  // Check in guest
  .post('/:id/check-in', async ({ params }) => {
    try {
      const guest = await GuestService.checkInGuest(params.id);
      return {
        success: true,
        data: guest
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }, {
    params: guestParams
  })

  // Check out guest
  .post('/:id/check-out', async ({ params }) => {
    try {
      const guest = await GuestService.checkOutGuest(params.id);
      return {
        success: true,
        data: guest
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }, {
    params: guestParams
  })

  // Mark guest as no-show
  .post('/:id/no-show', async ({ params }) => {
    try {
      const guest = await GuestService.markNoShow(params.id);
      return {
        success: true,
        data: guest
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }, {
    params: guestParams
  })

  // Bulk operations on guests
  .post('/bulk', async ({ body }) => {
    try {
      const result = await GuestService.bulkOperation(body);
      return {
        success: true,
        data: result
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }, {
    body: bulkGuestOperation
  })

  // Get daily guests
  .get('/daily', async ({ query }) => {
    try {
      const guests = await GuestService.getDailyGuests(query);
      return {
        success: true,
        data: guests
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }, {
    query: getDailyGuestsQuery
  });
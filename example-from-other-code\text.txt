Del codigo dentro de la carpeta example-from-other-code/office-managment, requiero pasar toda esa logica y codigo dentro del proyecto de nextjs, migrando desde mongoose y express a prisma y nextjs.

De ser posible, me gustaría que se utilizara elysiajs en lugar de express y que se integre en las rutas de nextjs para que si en un futuro se requiere separar la api de nextjs, se pueda hacer de manera sencilla y que no interfiera con el funcionamiento de nextjs.

Aqui hay documentación de como integrar elysiajs con nextjs: 

https://elysiajs.com/integrations/nextjs.html#integration-with-nextjs

Igualmente ya integré elysiajs con nextjs, dandote ejemplos para que puedas seguir esa misma estructura por modulos al igual que estan en el proyecto de office-managment
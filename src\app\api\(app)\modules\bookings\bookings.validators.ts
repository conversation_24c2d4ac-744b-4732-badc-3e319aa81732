import { t } from 'elysia';

export const createBooking = t.Object({
  resourceType: t.Union([t.Literal('MEETING_ROOM'), t.Literal('DESK')]),
  resourceId: t.String(),
  title: t.String(),
  description: t.Optional(t.String()),
  startDate: t.String(),
  endDate: t.String(),
  attendees: t.Optional(
    t.Object({
      internal: t.Optional(t.Array(t.String())),
      external: t.Optional(t.Array(t.String()))
    })
  )
});

export const updateBooking = t.Object({
  title: t.Optional(t.String()),
  description: t.Optional(t.String()),
  startDate: t.Optional(t.String()),
  endDate: t.Optional(t.String()),
  attendees: t.Optional(
    t.Object({
      internal: t.Optional(t.Array(t.String())),
      external: t.Optional(t.Array(t.String()))
    })
  )
});

export const getBookingsQuery = t.Object({
  page: t.Optional(t.Numeric()),
  limit: t.Optional(t.Numeric()),
  startDate: t.Optional(t.String()),
  endDate: t.Optional(t.String()),
  status: t.Optional(t.Union([
    t.Literal('CONFIRMED'),
    t.Literal('CANCELLED'),
    t.Literal('COMPLETED'),
    t.Literal('NO_SHOW')
  ])),
  resourceType: t.Optional(t.Union([t.Literal('MEETING_ROOM'), t.Literal('DESK')])),
  resourceId: t.Optional(t.String()),
  userId: t.Optional(t.String())
});

export const bookingParams = t.Object({
  id: t.String()
});

export const cancelBooking = t.Object({
  reason: t.Optional(t.String())
});

export const checkInBooking = t.Object({
  checkInTime: t.Optional(t.String())
});

export const checkOutBooking = t.Object({
  checkOutTime: t.Optional(t.String())
});

export const getStatsQuery = t.Object({
  startDate: t.Optional(t.String()),
  endDate: t.Optional(t.String()),
});

export type CreateBooking = typeof createBooking.static;
export type UpdateBooking = typeof updateBooking.static;
export type GetBookingsQuery = typeof getBookingsQuery.static;
export type BookingParams = typeof bookingParams.static;
export type CancelBooking = typeof cancelBooking.static;
export type CheckInBooking = typeof checkInBooking.static;
export type CheckOutBooking = typeof checkOutBooking.static;
export type GetStatsQuery = typeof getStatsQuery.static;

// src/app/page.tsx
'use client'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { authClient } from '@/lib/auth-client'
import { Building2, Calendar, Users, MapPin } from 'lucide-react'
import Image from 'next/image'

export default function LoginPage() {
  const handleGoogleLogin = async () => {
    // TODO: Implement Google OAuth with Better Auth
    // console.log('Google login clicked')
    // // Redirect to dashboard for now
    // window.location.href = '/dashboard'

    const response = await authClient.signIn.social({
      provider: 'google',
      callbackURL: window.location.origin + '/dashboard'
    })

    console.log('response', response)
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0 z-0">
        <Image
          src="/background.webp"
          alt="OneCarNow Office Background"
          fill
          className="object-cover"
          // unoptimized={true}
          // priority
        />
        {/* Overlay for better text readability */}
        <div className="absolute inset-0 bg-black/20" />
      </div>

      {/* Content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center p-6">
        <div className="w-full max-w-md">
          {/* Login Card with Blur Background */}
          <Card className="backdrop-blur-md bg-white/10 border-white/20 shadow-2xl">
            <CardHeader className="text-center space-y-4">
              <div className="flex justify-center">
                <div className="p-3 rounded-full bg-white/20 backdrop-blur-sm">
                  <Building2 className="w-8 h-8 text-white" />
                </div>
              </div>
              <div>
                <CardTitle className="text-2xl font-bold text-white">
                  OneCarNow Office
                </CardTitle>
                <CardDescription className="text-white/80 mt-2">
                  Sistema de Gestión de Oficinas
                </CardDescription>
              </div>
            </CardHeader>

            <CardContent className="space-y-6">
              {/* Features Preview */}
              <div className="space-y-3">
                <div className="flex items-center gap-3 text-white/90">
                  <Calendar className="w-5 h-5" />
                  <span className="text-sm">Reserva salas y escritorios</span>
                </div>
                <div className="flex items-center gap-3 text-white/90">
                  <Users className="w-5 h-5" />
                  <span className="text-sm">Gestiona invitados</span>
                </div>
                <div className="flex items-center gap-3 text-white/90">
                  <MapPin className="w-5 h-5" />
                  <span className="text-sm">Control de acceso</span>
                </div>
              </div>

              {/* Login Button */}
              <div className="space-y-4">
                <Button
                  onClick={handleGoogleLogin}
                  className="w-full bg-white text-gray-900 hover:bg-white/90 font-medium py-3 h-auto"
                  size="lg"
                >
                  <svg className="w-5 h-5 mr-3" viewBox="0 0 24 24">
                    <path
                      fill="currentColor"
                      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                    />
                    <path
                      fill="currentColor"
                      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                    />
                    <path
                      fill="currentColor"
                      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                    />
                    <path
                      fill="currentColor"
                      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                    />
                  </svg>
                  Continuar con Google
                </Button>

                <p className="text-xs text-white/70 text-center">
                  Solo empleados con email @onecarnow.com pueden acceder
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Additional Info */}
          <div className="mt-6 text-center">
            <p className="text-white/60 text-sm">
              ¿Necesitas ayuda? Contacta a IT Support
            </p>
          </div>
        </div>
      </div>

      {/* Bottom Branding */}
      <div className="absolute bottom-6 left-6 z-10">
        <div className="flex items-center gap-2 text-white/80">
          <Building2 className="w-5 h-5" />
          <span className="text-sm font-medium">OneCarNow</span>
        </div>
      </div>

      {/* Version Info */}
      <div className="absolute bottom-6 right-6 z-10">
        <p className="text-white/60 text-xs">v1.0.0</p>
      </div>
    </div>
  )
}

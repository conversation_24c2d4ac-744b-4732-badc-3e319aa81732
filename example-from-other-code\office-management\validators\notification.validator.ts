import { body, param } from 'express-validator';
import mongoose from 'mongoose';

export class NotificationValidator {
  static sendCustomNotification = [
    body('userId')
      .isMongoId()
      .withMessage('User ID must be a valid MongoDB ObjectId'),
    
    body('subject')
      .trim()
      .isLength({ min: 1, max: 200 })
      .withMessage('Subject must be between 1 and 200 characters'),
    
    body('message')
      .trim()
      .isLength({ min: 1, max: 2000 })
      .withMessage('Message must be between 1 and 2000 characters'),
    
    body('channels')
      .optional()
      .isArray()
      .withMessage('Channels must be an array')
      .custom((value) => {
        if (value && value.length > 0) {
          const validChannels = ['email', 'slack'];
          const invalidChannels = value.filter((channel: string) => !validChannels.includes(channel));
          if (invalidChannels.length > 0) {
            throw new Error('Invalid notification channels. Valid options: email, slack');
          }
        }
        return true;
      })
  ];

  static sendBookingReminder = [
    param('bookingId')
      .isMongoId()
      .withMessage('Booking ID must be a valid MongoDB ObjectId'),
    
    body('channels')
      .optional()
      .isArray()
      .withMessage('Channels must be an array')
      .custom((value) => {
        if (value && value.length > 0) {
          const validChannels = ['email', 'slack'];
          const invalidChannels = value.filter((channel: string) => !validChannels.includes(channel));
          if (invalidChannels.length > 0) {
            throw new Error('Invalid notification channels. Valid options: email, slack');
          }
        }
        return true;
      })
  ];

  static sendGuestInvitation = [
    param('guestId')
      .isMongoId()
      .withMessage('Guest ID must be a valid MongoDB ObjectId'),
    
    body('channels')
      .optional()
      .isArray()
      .withMessage('Channels must be an array')
      .custom((value) => {
        if (value && value.length > 0) {
          const validChannels = ['email', 'whatsapp'];
          const invalidChannels = value.filter((channel: string) => !validChannels.includes(channel));
          if (invalidChannels.length > 0) {
            throw new Error('Invalid notification channels. Valid options: email, whatsapp');
          }
        }
        return true;
      })
  ];

  static sendBulkNotifications = [
    body('userIds')
      .isArray({ min: 1 })
      .withMessage('User IDs array must contain at least one user ID')
      .custom((value) => {
        const invalidIds = value.filter((id: string) => !mongoose.Types.ObjectId.isValid(id));
        if (invalidIds.length > 0) {
          throw new Error('All user IDs must be valid MongoDB ObjectIds');
        }
        if (value.length > 100) {
          throw new Error('Cannot send notifications to more than 100 users at once');
        }
        return true;
      }),
    
    body('subject')
      .trim()
      .isLength({ min: 1, max: 200 })
      .withMessage('Subject must be between 1 and 200 characters'),
    
    body('message')
      .trim()
      .isLength({ min: 1, max: 2000 })
      .withMessage('Message must be between 1 and 2000 characters'),
    
    body('channels')
      .optional()
      .isArray()
      .withMessage('Channels must be an array')
      .custom((value) => {
        if (value && value.length > 0) {
          const validChannels = ['email', 'slack'];
          const invalidChannels = value.filter((channel: string) => !validChannels.includes(channel));
          if (invalidChannels.length > 0) {
            throw new Error('Invalid notification channels. Valid options: email, slack');
          }
        }
        return true;
      })
  ];

  static sendDepartmentNotification = [
    body('department')
      .trim()
      .isLength({ min: 1, max: 100 })
      .withMessage('Department must be between 1 and 100 characters'),
    
    body('subject')
      .trim()
      .isLength({ min: 1, max: 200 })
      .withMessage('Subject must be between 1 and 200 characters'),
    
    body('message')
      .trim()
      .isLength({ min: 1, max: 2000 })
      .withMessage('Message must be between 1 and 2000 characters'),
    
    body('channels')
      .optional()
      .isArray()
      .withMessage('Channels must be an array')
      .custom((value) => {
        if (value && value.length > 0) {
          const validChannels = ['email', 'slack'];
          const invalidChannels = value.filter((channel: string) => !validChannels.includes(channel));
          if (invalidChannels.length > 0) {
            throw new Error('Invalid notification channels. Valid options: email, slack');
          }
        }
        return true;
      })
  ];

  static getNotificationStatus = [
    param('type')
      .isIn(['booking', 'guest'])
      .withMessage('Notification type must be either booking or guest'),
    
    param('id')
      .isMongoId()
      .withMessage('ID must be a valid MongoDB ObjectId')
  ];

  static testNotificationServices = [
    body('email')
      .optional()
      .isEmail()
      .normalizeEmail()
      .withMessage('Email must be a valid email address'),
    
    body('slackUserId')
      .optional()
      .trim()
      .isLength({ min: 1 })
      .withMessage('Slack user ID cannot be empty'),
    
    body('phone')
      .optional()
      .matches(/^\+?[\d\s\-\(\)]+$/)
      .withMessage('Phone number format is invalid'),
    
    body()
      .custom((value) => {
        if (!value.email && !value.slackUserId && !value.phone) {
          throw new Error('At least one contact method (email, slackUserId, or phone) must be provided');
        }
        return true;
      })
  ];
}

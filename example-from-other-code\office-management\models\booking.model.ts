import mongoose, { Schema, Document } from 'mongoose';
import officeManagementDB from '../db';

export interface IBooking extends Document {
  userId: mongoose.Types.ObjectId;
  resourceType: 'meeting-room' | 'desk';
  resourceId: mongoose.Types.ObjectId;
  title: string;
  description?: string;
  startDate: Date;
  endDate: Date;
  status: 'confirmed' | 'cancelled' | 'completed' | 'no-show';
  attendees?: {
    internal: mongoose.Types.ObjectId[]; // Internal users
    external: string[]; // External emails
  };
  guests?: mongoose.Types.ObjectId[]; // Reference to Guest model
  notifications: {
    slack: {
      sent: boolean;
      messageId?: string;
      sentAt?: Date;
    };
    email: {
      sent: boolean;
      sentAt?: Date;
    };
  };
  cancellationReason?: string;
  cancelledAt?: Date;
  cancelledBy?: mongoose.Types.ObjectId;
  checkIn?: {
    time: Date;
    by: mongoose.Types.ObjectId;
  };
  checkOut?: {
    time: Date;
    by: mongoose.Types.ObjectId;
  };
  createdAt: Date;
  updatedAt: Date;
}

const BookingSchema = new Schema<IBooking>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    resourceType: {
      type: String,
      enum: ['meeting-room', 'desk'],
      required: true,
    },
    resourceId: {
      type: Schema.Types.ObjectId,
      required: true,
      refPath: function (this: IBooking) {
        return this.resourceType === 'meeting-room' ? 'MeetingRoom' : 'Desk';
      },
    },
    title: {
      type: String,
      required: true,
      trim: true,
      maxlength: 200,
    },
    description: {
      type: String,
      trim: true,
      maxlength: 1000,
    },
    startDate: {
      type: Date,
      required: true,
    },
    endDate: {
      type: Date,
      required: true,
      validate: {
        validator: function (this: IBooking, endDate: Date) {
          return endDate > this.startDate;
        },
        message: 'End date must be after start date',
      },
    },
    status: {
      type: String,
      enum: ['confirmed', 'cancelled', 'completed', 'no-show'],
      default: 'confirmed',
    },
    attendees: {
      internal: [
        {
          type: Schema.Types.ObjectId,
          ref: 'User',
        },
      ],
      external: [
        {
          type: String,
          validate: {
            validator: function (email: string) {
              return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
            },
            message: 'Invalid email format',
          },
        },
      ],
    },
    guests: [
      {
        type: Schema.Types.ObjectId,
        ref: 'Guest',
      },
    ],
    notifications: {
      slack: {
        sent: {
          type: Boolean,
          default: false,
        },
        messageId: String,
        sentAt: Date,
      },
      email: {
        sent: {
          type: Boolean,
          default: false,
        },
        sentAt: Date,
      },
    },
    cancellationReason: {
      type: String,
      trim: true,
      maxlength: 500,
    },
    cancelledAt: Date,
    cancelledBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    checkIn: {
      time: Date,
      by: {
        type: Schema.Types.ObjectId,
        ref: 'User',
      },
    },
    checkOut: {
      time: Date,
      by: {
        type: Schema.Types.ObjectId,
        ref: 'User',
      },
    },
  },
  {
    timestamps: true,
    toJSON: {
      transform: function (doc, ret) {
        delete ret.__v;
        return ret;
      },
    },
  }
);

// Indexes
BookingSchema.index({ userId: 1 });
BookingSchema.index({ resourceType: 1, resourceId: 1 });
BookingSchema.index({ startDate: 1, endDate: 1 });
BookingSchema.index({ status: 1 });
BookingSchema.index({ startDate: 1, status: 1 });

// Compound index for checking conflicts
BookingSchema.index({
  resourceType: 1,
  resourceId: 1,
  startDate: 1,
  endDate: 1,
  status: 1,
});

export const BookingModel = officeManagementDB.model<IBooking>('Booking', BookingSchema);

import { Router } from 'express';
import generalRoutes from './general-routes';
import authRouter from './modules/auth/routes/auth.routes';
import meetingRoomsRouter from './modules/meeting-rooms/routes/meeting-rooms.routes';
import desksRouter from './modules/desks/routes/desks.routes';
import bookingsRouter from './modules/bookings/routes/bookings.routes';
import guestsRouter from './routes/guest.routes';
import usersRouter from './routes/user.routes';
import notificationsRouter from './routes/notification.routes';

const officeManagementMainRouter = Router();

export const officeManagementUrl = '/office-management';

// General routes
officeManagementMainRouter.use(officeManagementUrl, generalRoutes);

// Authentication routes
officeManagementMainRouter.use(officeManagementUrl, authRouter);

// Module routes
officeManagementMainRouter.use(officeManagementUrl, meetingRoomsRouter);
officeManagementMainRouter.use(officeManagementUrl, desksRouter);
officeManagementMainRouter.use(officeManagementUrl, bookingsRouter);
officeManagementMainRouter.use(officeManagementUrl + '/guests', guestsRouter);
officeManagementMainRouter.use(officeManagementUrl + '/users', usersRouter);
officeManagementMainRouter.use(officeManagementUrl + '/notifications', notificationsRouter);

export default officeManagementMainRouter;

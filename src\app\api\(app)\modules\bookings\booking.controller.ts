import { Elysia } from 'elysia';
import { BookingService } from './booking.service';
import {
  createBooking,
  updateBooking,
  getBookingsQuery,
  bookingParams,
  cancelBooking,
  // checkInBooking,
  // checkOutBooking,
  getStatsQuery
} from './bookings.validators';
import { authMiddleware } from '../../middlewares/auth.middleware';

// export const bookingController = new Elysia({ prefix: '/bookings' })
export const bookingController = new Elysia({
  prefix: '/bookings',
  detail: {
    tags: ['Bookings'],
    description: 'Endpoints for managing bookings'
  }
})
  .use(authMiddleware)
  // Get all bookings
  .get('/', async ({ query, user }) => {
    try {
      const userId = user?.id;

      const data = { ...query, userId };

      const result = await BookingService.getAllBookings(data);
      return {
        success: true,
        data: result.data,
        pagination: result.pagination
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }, {
    query: getBookingsQuery
  })

  // Create new booking (requires userId from auth context)
  .post('/', async ({ body, headers }) => {
    try {
      // In a real app, you'd get userId from authentication middleware
      const userId = headers['x-user-id'] || 'temp-user-id';
      const booking = await BookingService.createBooking(body, userId);
      return {
        success: true,
        data: booking
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }, {
    body: createBooking
  })

  // Get booking by ID
  .get('/:id', async ({ params, headers }) => {
    try {
      const userId = headers['x-user-id'];
      const booking = await BookingService.getBookingById(params.id, userId);
      return {
        success: true,
        data: booking
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }, {
    params: bookingParams
  })

  // Update booking
  .put('/:id', async ({ params, body, headers }) => {
    try {
      const userId = headers['x-user-id'] || 'temp-user-id';
      const booking = await BookingService.updateBooking(params.id, body, userId);
      return {
        success: true,
        data: booking
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }, {
    params: bookingParams,
    body: updateBooking
  })

  // Cancel booking
  .post('/:id/cancel', async ({ params, body, headers }) => {
    try {
      const userId = headers['x-user-id'] || 'temp-user-id';
      const booking = await BookingService.cancelBooking(params.id, body, userId);
      return {
        success: true,
        data: booking
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }, {
    params: bookingParams,
    body: cancelBooking
  })

  // Check in booking
  .post('/:id/check-in', async ({ params, headers }) => {
    try {
      const userId = headers['x-user-id'] || 'temp-user-id';
      const booking = await BookingService.checkInBooking(params.id, userId);
      return {
        success: true,
        data: booking
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }, {
    params: bookingParams
  })

  // Check out booking
  .post('/:id/check-out', async ({ params, headers }) => {
    try {
      const userId = headers['x-user-id'] || 'temp-user-id';
      const booking = await BookingService.checkOutBooking(params.id, userId);
      return {
        success: true,
        data: booking
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }, {
    params: bookingParams
  })

  // Get booking statistics
  .get('/stats', async ({ query, user }) => {
    try {
      const userId = user?.id;
      const data = { ...query, userId };
      const stats = await BookingService.getBookingStats(data);
      return {
        success: true,
        data: stats
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }, {
    query: getStatsQuery
  })

  // Get today's bookings
  .get('/today', async () => {
    try {
      const bookings = await BookingService.getTodayBookings();
      return {
        success: true,
        data: bookings
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  })

  // Get user's bookings
  .get('/user/:userId', async ({ params, query }) => {
    try {
      const result = await BookingService.getUserBookings(params.userId, query);
      return {
        success: true,
        data: result.data,
        pagination: result.pagination
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  });

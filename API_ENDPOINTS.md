# API Endpoints Documentation

## Base URL
```
http://localhost:4002/api
```

## Authentication
Most endpoints require authentication. Include the user ID in the `x-user-id` header for now (this will be replaced with proper JWT authentication later).

## Response Format
All endpoints return responses in the following format:
```json
{
  "success": boolean,
  "data": any,
  "error": string,
  "pagination": {
    "page": number,
    "limit": number,
    "total": number,
    "totalPages": number
  }
}
```

## Endpoints

### 🏢 Desks

#### GET /api/desks
Get all desks with optional filtering and pagination.

**Query Parameters:**
- `page` (number, optional): Page number (default: 1)
- `limit` (number, optional): Items per page (default: 10)
- `search` (string, optional): Search by name or description
- `isActive` (boolean, optional): Filter by active status
- `floor` (string, optional): Filter by floor
- `zone` (string, optional): Filter by zone

**Example:**
```bash
GET /api/desks?page=1&limit=10&floor=2&isActive=true
```

#### POST /api/desks
Create a new desk.

**Body:**
```json
{
  "name": "Desk A1",
  "location": {
    "building": "Main Building",
    "floor": "2",
    "zone": "North Wing",
    "address": "123 Main St"
  },
  "description": "Standing desk with monitor",
  "isActive": true,
  "equipment": ["Monitor", "Keyboard", "Mouse"],
  "images": ["desk1.jpg", "desk2.jpg"]
}
```

#### GET /api/desks/:id
Get a specific desk by ID.

#### PUT /api/desks/:id
Update a desk.

#### DELETE /api/desks/:id
Delete a desk (only if no active bookings).

#### GET /api/desks/:id/availability
Check desk availability for a specific date.

**Query Parameters:**
- `date` (string, required): Date in ISO format
- `userId` (string, optional): User ID to exclude from conflict check

### 🏛️ Meeting Rooms

#### GET /api/meeting-rooms
Get all meeting rooms with optional filtering and pagination.

**Query Parameters:**
- `page` (number, optional): Page number
- `limit` (number, optional): Items per page
- `search` (string, optional): Search by name or description
- `isActive` (boolean, optional): Filter by active status
- `capacity` (number, optional): Minimum capacity required
- `floor` (string, optional): Filter by floor
- `amenities` (array, optional): Required amenities
- `equipment` (array, optional): Required equipment

#### POST /api/meeting-rooms
Create a new meeting room.

**Body:**
```json
{
  "name": "Conference Room A",
  "location": {
    "building": "Main Building",
    "floor": "3",
    "room": "301A",
    "address": "123 Main St"
  },
  "capacity": 12,
  "amenities": ["Whiteboard", "Projector"],
  "equipment": ["TV", "Conference Phone"],
  "description": "Large conference room",
  "isActive": true,
  "images": ["room1.jpg"],
  "bookingRules": {
    "minBookingDuration": 30,
    "maxBookingDuration": 480,
    "advanceBookingDays": 30,
    "minNoticeHours": 2
  },
  "availability": {
    "businessHours": {
      "start": "09:00",
      "end": "18:00"
    },
    "workingDays": [1, 2, 3, 4, 5],
    "timezone": "America/Mexico_City"
  }
}
```

#### GET /api/meeting-rooms/:id
Get a specific meeting room by ID.

#### PUT /api/meeting-rooms/:id
Update a meeting room.

#### DELETE /api/meeting-rooms/:id
Delete a meeting room (only if no active bookings).

#### GET /api/meeting-rooms/:id/availability
Check meeting room availability for a time range.

**Query Parameters:**
- `startDate` (string, required): Start date/time in ISO format
- `endDate` (string, required): End date/time in ISO format
- `excludeBookingId` (string, optional): Booking ID to exclude from conflict check

#### GET /api/meeting-rooms/:id/bookings
Get all bookings for a specific meeting room.

### 📅 Bookings

#### GET /api/bookings
Get all bookings with optional filtering and pagination.

**Query Parameters:**
- `page` (number, optional): Page number
- `limit` (number, optional): Items per page
- `startDate` (string, optional): Filter by start date
- `endDate` (string, optional): Filter by end date
- `status` (string, optional): Filter by status (CONFIRMED, CANCELLED, COMPLETED, NO_SHOW)
- `resourceType` (string, optional): Filter by resource type (MEETING_ROOM, DESK)
- `resourceId` (string, optional): Filter by specific resource
- `userId` (string, optional): Filter by user

#### POST /api/bookings
Create a new booking.

**Headers:**
- `x-user-id` (string, required): User ID making the booking

**Body:**
```json
{
  "resourceType": "MEETING_ROOM",
  "resourceId": "room-id-here",
  "title": "Team Meeting",
  "description": "Weekly team sync",
  "startDate": "2024-01-15T10:00:00Z",
  "endDate": "2024-01-15T11:00:00Z",
  "attendees": {
    "internal": ["user-id-1", "user-id-2"],
    "external": ["<EMAIL>"]
  }
}
```

#### GET /api/bookings/:id
Get a specific booking by ID.

#### PUT /api/bookings/:id
Update a booking.

#### POST /api/bookings/:id/cancel
Cancel a booking.

**Body:**
```json
{
  "reason": "Meeting cancelled due to schedule conflict"
}
```

#### POST /api/bookings/:id/check-in
Check in to a booking.

#### POST /api/bookings/:id/check-out
Check out from a booking.

#### GET /api/bookings/stats
Get booking statistics.

**Query Parameters:**
- `startDate` (string, optional): Start date for stats
- `endDate` (string, optional): End date for stats
- `userId` (string, optional): User-specific stats

#### GET /api/bookings/today
Get today's bookings.

#### GET /api/bookings/user/:userId
Get bookings for a specific user.

### 👥 Guests

#### GET /api/guests
Get all guests with optional filtering and pagination.

**Query Parameters:**
- `page` (number, optional): Page number
- `limit` (number, optional): Items per page
- `startDate` (string, optional): Filter by visit date range start
- `endDate` (string, optional): Filter by visit date range end
- `status` (string, optional): Filter by status (PENDING, CHECKED_IN, CHECKED_OUT, NO_SHOW)
- `hostId` (string, optional): Filter by host
- `visitDate` (string, optional): Filter by specific visit date
- `search` (string, optional): Search by name, email, or company

#### POST /api/guests
Create a new guest invitation.

**Headers:**
- `x-user-id` (string, required): Host user ID

**Body:**
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "company": "Example Corp",
  "visitDate": "2024-01-15",
  "visitTime": "14:30",
  "bookingId": "booking-id-here",
  "instructions": "Please bring ID"
}
```

#### GET /api/guests/:id
Get a specific guest by ID.

#### PUT /api/guests/:id
Update a guest.

#### DELETE /api/guests/:id
Delete a guest (only if not checked in).

#### POST /api/guests/:id/check-in
Check in a guest.

#### POST /api/guests/:id/check-out
Check out a guest.

#### POST /api/guests/:id/no-show
Mark a guest as no-show.

#### POST /api/guests/bulk
Perform bulk operations on multiple guests.

**Body:**
```json
{
  "guestIds": ["guest-id-1", "guest-id-2"],
  "operation": "check-in",
  "reason": "Bulk check-in for event"
}
```

#### GET /api/guests/daily
Get guests for a specific day.

**Query Parameters:**
- `date` (string, optional): Date in ISO format (default: today)
- `status` (string, optional): Filter by status
- `hostId` (string, optional): Filter by host

## Error Handling

All endpoints return appropriate HTTP status codes:
- `200` - Success
- `400` - Bad Request (validation errors)
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `500` - Internal Server Error

Error responses include a descriptive message:
```json
{
  "success": false,
  "error": "Desk not found"
}
```

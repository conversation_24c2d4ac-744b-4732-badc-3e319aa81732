import { prisma } from '@/db';
import { Create<PERSON>eetingRoom, UpdateMeetingRoom, GetMeetingRoomQuery, CheckAvailabilityQuery } from './meeting-rooms.validators';

export class MeetingRoomService {
  static async createMeetingRoom(data: CreateMeetingRoom) {
    try {
      const meetingRoom = await prisma.meetingRoom.create({
        data: {
          ...data,
          location: data.location as any,
          amenities: data.amenities || [],
          equipment: data.equipment || [],
          images: data.images || [],
          isActive: data.isActive ?? true,
          bookingRules: data.bookingRules as any || {
            minBookingDuration: 30,
            maxBookingDuration: 480,
            advanceBookingDays: 30,
            minNoticeHours: 2
          },
          availability: data.availability as any || {
            businessHours: { start: '09:00', end: '18:00' },
            workingDays: [1, 2, 3, 4, 5],
            timezone: 'America/Mexico_City'
          }
        }
      });
      return meetingRoom;
    } catch (error: any) {
      if (error.code === 'P2002') {
        throw new Error('A meeting room with this name already exists');
      }
      throw new Error(`Failed to create meeting room: ${error.message}`);
    }
  }

  static async getAllMeetingRooms(query: GetMeetingRoomQuery = {}) {
    const {
      page = 1,
      limit = 10,
      search,
      isActive,
      capacity,
      floor,
      amenities,
      equipment
    } = query;

    const skip = (page - 1) * limit;
    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (isActive !== undefined) {
      where.isActive = isActive;
    }

    if (capacity) {
      where.capacity = { gte: capacity };
    }

    if (floor) {
      where.location = {
        path: ['floor'],
        equals: floor
      };
    }

    if (amenities && amenities.length > 0) {
      where.amenities = {
        hasEvery: amenities
      };
    }

    if (equipment && equipment.length > 0) {
      where.equipment = {
        hasEvery: equipment
      };
    }

    const [meetingRooms, total] = await Promise.all([
      prisma.meetingRoom.findMany({
        where,
        skip,
        take: limit,
        orderBy: { name: 'asc' },
        include: {
          bookings: {
            where: {
              status: 'CONFIRMED',
              startDate: { gte: new Date() }
            },
            take: 5,
            orderBy: { startDate: 'asc' }
          }
        }
      }),
      prisma.meetingRoom.count({ where })
    ]);

    return {
      data: meetingRooms,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  static async getMeetingRoomById(id: string) {
    const meetingRoom = await prisma.meetingRoom.findUnique({
      where: { id },
      include: {
        bookings: {
          where: {
            status: 'CONFIRMED'
          },
          include: {
            user: {
              select: { id: true, name: true, email: true }
            }
          },
          orderBy: { startDate: 'asc' }
        }
      }
    });

    if (!meetingRoom) {
      throw new Error('Meeting room not found');
    }

    return meetingRoom;
  }

  static async updateMeetingRoom(id: string, data: UpdateMeetingRoom) {
    try {
      const meetingRoom = await prisma.meetingRoom.update({
        where: { id },
        data: {
          ...data,
          location: data.location as any,
          bookingRules: data.bookingRules as any,
          availability: data.availability as any
        }
      });
      return meetingRoom;
    } catch (error: any) {
      if (error.code === 'P2025') {
        throw new Error('Meeting room not found');
      }
      if (error.code === 'P2002') {
        throw new Error('A meeting room with this name already exists');
      }
      throw new Error(`Failed to update meeting room: ${error.message}`);
    }
  }

  static async deleteMeetingRoom(id: string) {
    try {
      // Check if meeting room has active bookings
      const activeBookings = await prisma.booking.count({
        where: {
          meetingRoomId: id,
          status: 'CONFIRMED',
          startDate: { gte: new Date() }
        }
      });

      if (activeBookings > 0) {
        throw new Error('Cannot delete meeting room with active bookings');
      }

      await prisma.meetingRoom.delete({
        where: { id }
      });

      return { success: true };
    } catch (error: any) {
      if (error.code === 'P2025') {
        throw new Error('Meeting room not found');
      }
      throw new Error(`Failed to delete meeting room: ${error.message}`);
    }
  }

  static async checkAvailability(id: string, query: CheckAvailabilityQuery) {
    const { startDate, endDate, excludeBookingId } = query;
    const start = new Date(startDate);
    const end = new Date(endDate);

    const where: any = {
      meetingRoomId: id,
      status: 'CONFIRMED',
      OR: [
        {
          AND: [
            { startDate: { lte: start } },
            { endDate: { gt: start } }
          ]
        },
        {
          AND: [
            { startDate: { lt: end } },
            { endDate: { gte: end } }
          ]
        },
        {
          AND: [
            { startDate: { gte: start } },
            { endDate: { lte: end } }
          ]
        }
      ]
    };

    if (excludeBookingId) {
      where.id = { not: excludeBookingId };
    }

    const conflictingBookings = await prisma.booking.findMany({
      where,
      include: {
        user: {
          select: { name: true, email: true }
        }
      }
    });

    return {
      available: conflictingBookings.length === 0,
      conflicts: conflictingBookings.map(booking => ({
        bookingId: booking.id,
        title: booking.title,
        user: booking.user,
        startDate: booking.startDate,
        endDate: booking.endDate
      }))
    };
  }

  static async getMeetingRoomBookings(roomId: string, query: any = {}) {
    const {
      page = 1,
      limit = 10,
      startDate,
      endDate,
      status
    } = query;

    const skip = (page - 1) * limit;
    const where: any = {
      meetingRoomId: roomId
    };

    if (startDate || endDate) {
      where.startDate = {};
      if (startDate) where.startDate.gte = new Date(startDate);
      if (endDate) where.startDate.lte = new Date(endDate);
    }

    if (status) {
      where.status = status;
    }

    const [bookings, total] = await Promise.all([
      prisma.booking.findMany({
        where,
        skip,
        take: limit,
        include: {
          user: {
            select: { id: true, name: true, email: true }
          },
          guests: true
        },
        orderBy: { startDate: 'desc' }
      }),
      prisma.booking.count({ where })
    ]);

    return {
      data: bookings,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }
}

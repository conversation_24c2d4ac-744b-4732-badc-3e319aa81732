# OCN House - Demo Workflow
# Este archivo demuestra el flujo completo paso a paso para:
# 1. Crear Meeting Rooms
# 2. Crear Desks  
# 3. Hacer reservas/bookings
# 
# IMPORTANTE: Reemplaza los IDs generados en cada paso para el siguiente

@baseUrl = http://localhost:4002
@contentType = application/json
@cookie = better-auth.session_token=KD8i6hPSS5TIBqHunp5szwPDkjmvT515.PDF0YAUs%2FqkiXYIiczZEdePFyLHCacL6i3eL201wtig%3D

###############################################################################
# PASO 1: CREAR MEETING ROOMS
###############################################################################

### 1.1 Crear Meeting Room Básica
POST {{baseUrl}}/api/meeting-rooms
Content-Type: {{contentType}}
Cookie: {{cookie}}

{
  "name": "Sala de Juntas Principal",
  "location": {
    "building": "Edificio Principal",
    "floor": "2",
    "room": "201"
  },
  "capacity": 8,
  "description": "Sala principal para reuniones ejecutivas",
  "isActive": true
}

### 1.2 Crear Meeting Room Completa con todas las opciones
POST {{baseUrl}}/api/meeting-rooms
Content-Type: {{contentType}}
Cookie: {{cookie}}

{
  "name": "Sala de Conferencias Tech",
  "location": {
    "building": "Torre Norte",
    "floor": "3",
    "room": "305",
    "address": "Av. Revolución 123, CDMX"
  },
  "capacity": 12,
  "amenities": [
    "Projector",
    "Whiteboard", 
    "Video Conference",
    "Smart TV",
    "Sound System",
    "Air Conditioning"
  ],
  "equipment": [
    "Projector 4K",
    "Wireless Microphone",
    "Laptop HDMI Cable",
    "Whiteboard Markers"
  ],
  "description": "Sala equipada con tecnología avanzada para videoconferencias",
  "isActive": true,
  "images": [
    "https://example.com/sala-tech-1.jpg",
    "https://example.com/sala-tech-2.jpg"
  ],
  "bookingRules": {
    "minBookingDuration": 30,
    "maxBookingDuration": 480,
    "advanceBookingDays": 7,
    "minNoticeHours": 2
  },
  "availability": {
    "businessHours": {
      "start": "08:00",
      "end": "20:00"
    },
    "workingDays": [1, 2, 3, 4, 5],
    "timezone": "America/Mexico_City"
  }
}

### 1.3 Crear Meeting Room Pequeña
POST {{baseUrl}}/api/meeting-rooms
Content-Type: {{contentType}}

{
  "name": "Sala Focus",
  "location": {
    "building": "Edificio Principal",
    "floor": "1",
    "room": "105"
  },
  "capacity": 4,
  "amenities": ["Whiteboard", "WiFi"],
  "description": "Sala pequeña para reuniones íntimas y sesiones de brainstorming",
  "isActive": true
}

###############################################################################
# PASO 2: CREAR DESKS
###############################################################################

### 2.1 Crear Desk Básico
POST {{baseUrl}}/api/desks
Content-Type: {{contentType}}
Cookie: {{cookie}}

{
  "name": "Escritorio A-01",
  "location": {
    "building": "Torre Principal",
    "floor": "2",
    "zone": "Norte"
  },
  "description": "Escritorio estándar con vista al jardín",
  "isActive": true
}

### 2.2 Crear Desk Completo
POST {{baseUrl}}/api/desks
Content-Type: {{contentType}}

{
  "name": "Escritorio Premium B-15",
  "location": {
    "building": "Torre Norte",
    "floor": "3",
    "zone": "Sur",
    "address": "Av. Revolución 123, CDMX"
  },
  "description": "Escritorio premium con equipamiento completo",
  "equipment": [
    "Monitor 27 pulgadas",
    "Teclado mecánico",
    "Mouse ergonómico",
    "Hub USB-C",
    "Lámpara LED",
    "Soporte para laptop"
  ],
  "images": [
    "https://example.com/desk-premium-1.jpg",
    "https://example.com/desk-premium-2.jpg"
  ],
  "isActive": true
}

### 2.3 Crear Desk Standing
POST {{baseUrl}}/api/desks
Content-Type: {{contentType}}

{
  "name": "Standing Desk C-08",
  "location": {
    "building": "Edificio Principal",
    "floor": "1",
    "zone": "Centro"
  },
  "description": "Escritorio de pie ajustable para trabajo dinámico",
  "equipment": [
    "Monitor ajustable",
    "Teclado inalámbrico",
    "Mouse vertical",
    "Tapete anti-fatiga"
  ],
  "isActive": true
}

###############################################################################
# PASO 3: VERIFICAR RECURSOS CREADOS
###############################################################################

@meetingRoomId = cmcmi77ar0000v65osr15q383
@deskId = cmcmipd4t0003v65oisasb8sk

### 3.1 Listar todas las Meeting Rooms
GET {{baseUrl}}/api/meeting-rooms
Content-Type: {{contentType}}
Cookie: {{cookie}}

### 3.2 Listar todos los Desks
GET {{baseUrl}}/api/desks
Content-Type: {{contentType}}
Cookie: {{cookie}}

### 3.3 Obtener Meeting Room específica (reemplazar ID)
GET {{baseUrl}}/api/meeting-rooms/{{meetingRoomId}}
Content-Type: {{contentType}}
Cookie: {{cookie}}

### 3.4 Obtener Desk específico (reemplazar ID)
GET {{baseUrl}}/api/desks/{{deskId}}
Content-Type: {{contentType}}
Cookie: {{cookie}}

###############################################################################
# PASO 4: VERIFICAR DISPONIBILIDAD
###############################################################################

### 4.1 Verificar disponibilidad de Meeting Room (reemplazar ID)
GET {{baseUrl}}/api/meeting-rooms/{{meetingRoomId}}/availability?startDate=2025-07-20T09:00:00.000Z&endDate=2025-07-20T11:00:00.000Z
Content-Type: {{contentType}}
Cookie: {{cookie}}

### 4.2 Verificar disponibilidad de Desk (reemplazar ID)
GET {{baseUrl}}/api/desks/DESK_ID_AQUI/availability?date=2024-12-10
Content-Type: {{contentType}}

###############################################################################
# PASO 5: CREAR BOOKINGS/RESERVAS
###############################################################################

### 5.1 Reservar Meeting Room (reemplazar MEETING_ROOM_ID)
POST {{baseUrl}}/api/bookings
Content-Type: {{contentType}}

{
  "resourceType": "MEETING_ROOM",
  "resourceId": "{{meetingRoomId}}",
  "title": "Reunión de Planificación Q1 2025",
  "description": "Reunión para planificar objetivos y estrategias del primer trimestre",
  "startDate": "2024-12-10T09:00:00.000Z",
  "endDate": "2024-12-10T11:00:00.000Z",
  "attendees": {
    "internal": ["<EMAIL>", "<EMAIL>"],
    "external": ["<EMAIL>"]
  }
}

### 5.2 Reservar Desk (reemplazar DESK_ID)
POST {{baseUrl}}/api/bookings
Content-Type: {{contentType}}

{
  "resourceType": "DESK",
  "resourceId": "DESK_ID_AQUI", 
  "title": "Trabajo Remoto - Desarrollo Frontend",
  "description": "Sesión de desarrollo concentrado en nuevas funcionalidades",
  "startDate": "2024-12-10T08:00:00.000Z",
  "endDate": "2024-12-10T17:00:00.000Z"
}

### 5.3 Reservar Meeting Room para videoconferencia
POST {{baseUrl}}/api/bookings
Content-Type: {{contentType}}

{
  "resourceType": "MEETING_ROOM",
  "resourceId": "MEETING_ROOM_ID_AQUI",
  "title": "Videoconferencia con Equipo Internacional",
  "description": "Reunión semanal con equipos de desarrollo en diferentes zonas horarias",
  "startDate": "2024-12-11T14:00:00.000Z",
  "endDate": "2024-12-11T15:30:00.000Z",
  "attendees": {
    "internal": ["<EMAIL>", "<EMAIL>"],
    "external": ["<EMAIL>", "<EMAIL>"]
  }
}

###############################################################################
# PASO 6: GESTIONAR BOOKINGS
###############################################################################

### 6.1 Listar todas las reservas
GET {{baseUrl}}/api/bookings
Content-Type: {{contentType}}

### 6.2 Listar reservas por fecha
GET {{baseUrl}}/api/bookings?startDate=2024-12-10T00:00:00.000Z&endDate=2024-12-10T23:59:59.000Z
Content-Type: {{contentType}}

### 6.3 Listar reservas de Meeting Rooms solamente
GET {{baseUrl}}/api/bookings?resourceType=MEETING_ROOM
Content-Type: {{contentType}}

### 6.4 Obtener estadísticas de reservas
GET {{baseUrl}}/api/bookings/stats
Content-Type: {{contentType}}

### 6.5 Obtener reservas de hoy
GET {{baseUrl}}/api/bookings/today
Content-Type: {{contentType}}

### 6.6 Obtener booking específico (reemplazar ID)
GET {{baseUrl}}/api/bookings/BOOKING_ID_AQUI
Content-Type: {{contentType}}

### 6.7 Actualizar booking (reemplazar ID)
PUT {{baseUrl}}/api/bookings/BOOKING_ID_AQUI
Content-Type: {{contentType}}

{
  "title": "Reunión de Planificación Q1 2025 - ACTUALIZADA",
  "description": "Reunión actualizada con agenda extendida",
  "startDate": "2024-12-10T09:00:00.000Z",
  "endDate": "2024-12-10T12:00:00.000Z"
}

### 6.8 Cancelar booking (reemplazar ID)
POST {{baseUrl}}/api/bookings/BOOKING_ID_AQUI/cancel
Content-Type: {{contentType}}

{
  "reason": "Reunión pospuesta por conflicto de horarios"
}

###############################################################################
# NOTAS IMPORTANTES:
###############################################################################

# 1. TIPOS DE DATOS:
#    - capacity, minBookingDuration, maxBookingDuration, etc. deben ser NUMBER, no string
#    - startDate y endDate deben estar en formato ISO 8601
#    - workingDays es array de números (0=Domingo, 1=Lunes, etc.)

# 2. CAMPOS REQUERIDOS:
#    Meeting Room: name, location.floor, location.room, capacity
#    Desk: name, location.floor, location.zone  
#    Booking: resourceType, resourceId, title, startDate, endDate

# 3. VALORES ENUM:
#    resourceType: "MEETING_ROOM" o "DESK"
#    status: "CONFIRMED", "CANCELLED", "COMPLETED", "NO_SHOW"

# 4. FECHAS:
#    Usar formato ISO 8601: "2024-12-10T09:00:00.000Z"
#    Las fechas deben ser futuras para crear bookings

# 5. IDs:
#    Reemplazar todos los placeholders "ID_AQUI" con IDs reales obtenidos de las respuestas

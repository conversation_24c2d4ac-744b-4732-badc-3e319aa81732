'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Building,
  Users,
  MapPin,
  Search,
  Plus,
  Filter,
  Calendar,
  Clock,
  Wifi,
  Monitor,
  Coffee
} from 'lucide-react';
import Link from 'next/link';
import { useMeetingRooms } from '@/lib/hooks/use-meeting-rooms';
import { MeetingRoomFilters } from '@/lib/hooks/use-meeting-rooms';

export default function MeetingRoomsPage() {
  const [filters, setFilters] = useState<MeetingRoomFilters>({
    page: 1,
    limit: 12,
    isActive: true
  });
  const [search, setSearch] = useState('');

  const { data: meetingRooms, isLoading, error } = useMeetingRooms({
    ...filters,
    search: search || undefined
  });

  const handleSearchChange = (value: string) => {
    setSearch(value);
    setFilters(prev => ({ ...prev, search: value || undefined, page: 1 }));
  };

  const getAmenityIcon = (amenity: string) => {
    switch (amenity.toLowerCase()) {
      case 'wifi':
        return <Wifi className="h-4 w-4" />;
      case 'projector':
      case 'tv':
      case 'monitor':
        return <Monitor className="h-4 w-4" />;
      case 'coffee':
        return <Coffee className="h-4 w-4" />;
      default:
        return <Building className="h-4 w-4" />;
    }
  };

  if (error) {
    return (
      <div className="flex flex-col gap-6 p-6">
        <div className="text-center py-12">
          <Building className="mx-auto h-12 w-12 text-muted-foreground" />
          <h3 className="mt-2 text-lg font-semibold">Error loading meeting rooms</h3>
          <p className="mt-1 text-sm text-muted-foreground">
            Please try again later.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
      <div className="max-w-7xl mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Salas de Reuniones</h1>
            <p className="text-gray-600">
              Explora y reserva espacios de reunión disponibles
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" className="rounded-xl border-gray-300 text-gray-700 hover:bg-gray-50">
              <Filter className="mr-2 h-4 w-4" />
              Filtros
            </Button>
            <Button variant="outline" asChild className="rounded-xl border-gray-300 text-gray-700 hover:bg-gray-50">
              <Link href="/dashboard/bookings/new">
                <Calendar className="mr-2 h-4 w-4" />
                Nueva Reserva
              </Link>
            </Button>
            <Button asChild className="rounded-xl bg-purple-600 hover:bg-purple-700 text-white">
              <Link href="/dashboard/rooms/new">
                <Plus className="mr-2 h-4 w-4" />
                Crear Sala
              </Link>
            </Button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Buscar salas de reuniones..."
              value={search}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pl-10 rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
            />
          </div>
        </div>

        {/* Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card className="border-0 shadow-lg rounded-2xl bg-white/80 backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-700">Total Salas</CardTitle>
              <Building className="h-4 w-4 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">
                {meetingRooms?.pagination?.total || 0}
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg rounded-2xl bg-white/80 backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-700">Disponibles Ahora</CardTitle>
              <Clock className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">
                {meetingRooms?.data?.filter((room: any) => room.isActive).length || 0}
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg rounded-2xl bg-white/80 backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-700">Salas Grandes</CardTitle>
              <Users className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">
                {meetingRooms?.data?.filter((room: any) => room.capacity >= 10).length || 0}
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg rounded-2xl bg-white/80 backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-700">Con Proyector</CardTitle>
              <Monitor className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">
                {meetingRooms?.data?.filter((room: any) =>
                  room.amenities?.includes('Projector') || room.equipment?.includes('Projector')
                ).length || 0}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Meeting Rooms Grid */}
        {isLoading ? (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {[...Array(6)].map((_, i) => (
              <Card key={i} className="border-0 shadow-lg rounded-2xl bg-white/80 backdrop-blur-sm">
                <CardHeader>
                  <Skeleton className="h-6 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-2/3" />
                    <div className="flex gap-2">
                      <Skeleton className="h-6 w-16" />
                      <Skeleton className="h-6 w-16" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : meetingRooms?.data?.length === 0 ? (
          <div className="text-center py-12">
            <Building className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-lg font-semibold text-gray-900">No se encontraron salas de reuniones</h3>
            <p className="mt-1 text-sm text-gray-600">
              {search ? 'Intenta ajustar tus criterios de búsqueda.' : 'No hay salas de reuniones disponibles.'}
            </p>
          </div>
        ) : (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {meetingRooms?.data?.map((room: any) => (
              <Card key={room.id} className="border-0 shadow-lg rounded-2xl bg-white/80 backdrop-blur-sm hover:shadow-xl transition-all duration-300 hover:scale-105 flex flex-col h-full">
                <CardHeader className="pb-3 flex-shrink-0">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0 pr-2">
                      <CardTitle className="text-lg text-gray-900 truncate" title={room.name}>
                        {room.name}
                      </CardTitle>
                      <CardDescription className="flex items-center gap-1 text-gray-600 truncate">
                        <MapPin className="h-3 w-3 flex-shrink-0" />
                        <span className="truncate">
                          Piso {room.location.floor}, {room.location.room}
                        </span>
                      </CardDescription>
                    </div>
                    <Badge
                      variant={room.isActive ? "default" : "secondary"}
                      className={`rounded-full flex-shrink-0 ${room.isActive
                        ? 'bg-green-100 text-green-700 hover:bg-green-200'
                        : 'bg-gray-100 text-gray-700'
                        }`}
                    >
                      {room.isActive ? "Disponible" : "Inactiva"}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="flex-1 flex flex-col justify-between space-y-4">
                  <div className="space-y-4">
                    {/* Capacity */}
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-purple-600 flex-shrink-0" />
                      <span className="text-sm text-gray-700">Hasta {room.capacity} personas</span>
                    </div>

                    {/* Description */}
                    <div className="min-h-[2.5rem]">
                      {room.description ? (
                        <p className="text-sm text-gray-600 line-clamp-2" title={room.description}>
                          {room.description}
                        </p>
                      ) : (
                        <p className="text-sm text-gray-400 italic">Sin descripción</p>
                      )}
                    </div>

                    {/* Amenities */}
                    <div className="min-h-[2rem]">
                      {room.amenities?.length > 0 ? (
                        <div className="flex flex-wrap gap-1">
                          {room.amenities.slice(0, 3).map((amenity: string) => (
                            <Badge key={amenity} variant="outline" className="text-xs rounded-full border-purple-200 text-purple-700">
                              {getAmenityIcon(amenity)}
                              <span className="ml-1 truncate max-w-[80px]" title={amenity}>{amenity}</span>
                            </Badge>
                          ))}
                          {room.amenities.length > 3 && (
                            <Badge variant="outline" className="text-xs rounded-full border-gray-200 text-gray-600">
                              +{room.amenities.length - 3} más
                            </Badge>
                          )}
                        </div>
                      ) : (
                        <p className="text-xs text-gray-400 italic">Sin amenidades</p>
                      )}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2 pt-2 flex-shrink-0">
                    <Button size="sm" className="flex-1 rounded-xl bg-purple-600 hover:bg-purple-700 text-white" asChild>
                      <Link href={`/dashboard/bookings/new?roomId=${room.id}`}>
                        <Calendar className="mr-2 h-4 w-4" />
                        Reservar Ahora
                      </Link>
                    </Button>
                    <Button size="sm" variant="outline" className="rounded-xl border-gray-300 text-gray-700 hover:bg-gray-50" asChild>
                      <Link href={`/dashboard/rooms/${room.id}`}>
                        Ver Detalles
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Pagination */}
        {meetingRooms?.pagination && meetingRooms.pagination.totalPages > 1 && (
          <div className="flex justify-center gap-2">
            <Button
              variant="outline"
              className="rounded-xl border-gray-300 text-gray-700 hover:bg-gray-50"
              disabled={meetingRooms.pagination.page === 1}
              onClick={() => setFilters(prev => ({ ...prev, page: prev.page! - 1 }))}
            >
              Anterior
            </Button>
            <span className="flex items-center px-4 text-sm text-gray-600">
              Página {meetingRooms.pagination.page} de {meetingRooms.pagination.totalPages}
            </span>
            <Button
              variant="outline"
              className="rounded-xl border-gray-300 text-gray-700 hover:bg-gray-50"
              disabled={meetingRooms.pagination.page === meetingRooms.pagination.totalPages}
              onClick={() => setFilters(prev => ({ ...prev, page: prev.page! + 1 }))}
            >
              Siguiente
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}

generator client {
  provider = "prisma-client-js"
  // output   = "../src/generated/prisma"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

model User {
  id            String    @id @map("_id")
  name          String
  email         String
  emailVerified Boolean
  image         String?
  role          UserRole  @default(USER)
  department    String?
  isActive      Boolean   @default(true)
  slackUserId   String?
  createdAt     DateTime
  updatedAt     DateTime
  sessions      Session[]
  accounts      Account[]
  bookings      Booking[]
  guestsHosted  Guest[]   @relation("HostedGuests")
  checkIns      Booking[] @relation("CheckInUser")
  checkOuts     Booking[] @relation("CheckOutUser")
  cancelledBookings Booking[] @relation("CancelledByUser")

  @@unique([email])
  @@map("user")
}

enum UserRole {
  USER
  ADMIN
  RECEPTIONIST
}

enum ResourceType {
  MEETING_ROOM
  DESK
}

enum BookingStatus {
  CONFIRMED
  CANCELLED
  COMPLETED
  NO_SHOW
}

enum GuestStatus {
  PENDING
  CHECKED_IN
  CHECKED_OUT
  NO_SHOW
}

model Session {
  id        String   @id @map("_id")
  expiresAt DateTime
  token     String
  createdAt DateTime
  updatedAt DateTime
  ipAddress String?
  userAgent String?
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([token])
  @@map("session")
}

model Account {
  id                    String    @id @map("_id")
  accountId             String
  providerId            String
  userId                String
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  createdAt             DateTime
  updatedAt             DateTime

  @@map("account")
}

model Verification {
  id         String    @id @map("_id")
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime?
  updatedAt  DateTime?

  @@map("verification")
}

model MeetingRoom {
  id          String   @id @default(cuid()) @map("_id")
  name        String   @unique
  location    Json     // { building?: string, floor: string, room: string, address?: string }
  capacity    Int
  amenities   String[]
  equipment   String[]
  description String?
  isActive    Boolean  @default(true)
  images      String[]
  bookingRules Json    // { minBookingDuration: number, maxBookingDuration: number, advanceBookingDays: number, minNoticeHours: number }
  availability Json    // { businessHours: { start: string, end: string }, workingDays: number[], timezone: string }
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  bookings    Booking[]

  @@map("meeting_room")
}

model Desk {
  id          String   @id @default(cuid()) @map("_id")
  name        String   @unique
  location    Json     // { building?: string, floor: string, zone: string, address?: string }
  description String?
  isActive    Boolean  @default(true)
  equipment   String[]
  images      String[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  bookings    Booking[]

  @@map("desk")
}

model Booking {
  id                String        @id @default(cuid()) @map("_id")
  userId            String
  user              User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  resourceType      ResourceType
  resourceId        String
  meetingRoom       MeetingRoom?  @relation(fields: [meetingRoomId], references: [id], onDelete: Cascade)
  meetingRoomId     String?
  desk              Desk?         @relation(fields: [deskId], references: [id], onDelete: Cascade)
  deskId            String?
  title             String
  description       String?
  startDate         DateTime
  endDate           DateTime
  status            BookingStatus @default(CONFIRMED)
  attendees         Json?         // { internal: string[], external: string[] }
  guests            Guest[]
  notifications     Json          @default("{\"slack\":{\"sent\":false},\"email\":{\"sent\":false}}")
  cancellationReason String?
  cancelledAt       DateTime?
  cancelledBy       User?         @relation("CancelledByUser", fields: [cancelledById], references: [id])
  cancelledById     String?
  checkIn           Json?         // { time: DateTime, by: string }
  checkOut          Json?         // { time: DateTime, by: string }
  checkInUser       User?         @relation("CheckInUser", fields: [checkInUserId], references: [id])
  checkInUserId     String?
  checkOutUser      User?         @relation("CheckOutUser", fields: [checkOutUserId], references: [id])
  checkOutUserId    String?
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt

  @@map("booking")
}

model Guest {
  id              String      @id @default(cuid()) @map("_id")
  name            String
  email           String?
  phone           String?
  company         String?
  visitDate       DateTime
  visitTime       String      // "HH:MM" format
  hostId          String
  host            User        @relation("HostedGuests", fields: [hostId], references: [id], onDelete: Cascade)
  bookingId       String?
  booking         Booking?    @relation(fields: [bookingId], references: [id], onDelete: SetNull)
  status          GuestStatus @default(PENDING)
  checkInTime     DateTime?
  checkOutTime    DateTime?
  instructions    String?
  notifications   Json        @default("{\"email\":{\"sent\":false},\"whatsapp\":{\"sent\":false}}")
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  @@map("guest")
}

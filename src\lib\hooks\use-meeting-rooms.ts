import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import server from '@/app/api/server';
import { toast } from 'sonner';

// Types
export interface MeetingRoom {
  id: string;
  name: string;
  location: {
    building?: string;
    floor: string;
    room: string;
    address?: string;
  };
  capacity: number;
  amenities: string[];
  equipment: string[];
  description?: string;
  isActive: boolean;
  images: string[];
  bookingRules: {
    minBookingDuration?: number;
    maxBookingDuration?: number;
    advanceBookingDays?: number;
    minNoticeHours?: number;
  };
  availability: {
    businessHours?: {
      start: string;
      end: string;
    };
    workingDays?: number[];
    timezone?: string;
  };
  bookings?: any[];
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateMeetingRoomInput {
  name: string;
  location: {
    building?: string;
    floor: string;
    room: string;
    address?: string;
  };
  capacity: number;
  amenities?: string[];
  equipment?: string[];
  description?: string;
  isActive?: boolean;
  images?: string[];
  bookingRules?: {
    minBookingDuration?: number;
    maxBookingDuration?: number;
    advanceBookingDays?: number;
    minNoticeHours?: number;
  };
  availability?: {
    businessHours?: {
      start: string;
      end: string;
    };
    workingDays?: number[];
    timezone?: string;
  };
}

export interface MeetingRoomFilters {
  page?: number;
  limit?: number;
  search?: string;
  isActive?: boolean;
  capacity?: number;
  floor?: string;
  amenities?: string[];
  equipment?: string[];
}

// Query Keys
export const meetingRoomKeys = {
  all: ['meeting-rooms'] as const,
  lists: () => [...meetingRoomKeys.all, 'list'] as const,
  list: (filters: MeetingRoomFilters) => [...meetingRoomKeys.lists(), filters] as const,
  details: () => [...meetingRoomKeys.all, 'detail'] as const,
  detail: (id: string) => [...meetingRoomKeys.details(), id] as const,
  availability: (id: string, params: any) => [...meetingRoomKeys.detail(id), 'availability', params] as const,
  bookings: (id: string, params: any) => [...meetingRoomKeys.detail(id), 'bookings', params] as const,
};

// Hooks
export function useMeetingRooms(filters: MeetingRoomFilters = {}) {
  return useQuery({
    queryKey: meetingRoomKeys.list(filters),
    queryFn: async () => {
      const response = await server.api['meeting-rooms'].get({
        query: filters
      });

      if (response.error) {
        throw new Error('Failed to fetch meeting rooms');
      }

      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useMeetingRoom(id: string) {
  return useQuery({
    queryKey: meetingRoomKeys.detail(id),
    queryFn: async () => {
      const response = await server.api['meeting-rooms']({ id }).get();

      if (response.error) {
        throw new Error('Failed to fetch meeting room');
      }

      return response.data;
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
}

export function useMeetingRoomAvailability(id: string, startDate: string, endDate: string) {
  return useQuery({
    queryKey: meetingRoomKeys.availability(id, { startDate, endDate }),
    queryFn: async () => {
      const response = await server.api['meeting-rooms']({ id }).availability.get({
        query: { startDate, endDate }
      });

      if (response.error) {
        throw new Error('Failed to check availability');
      }

      return response.data;
    },
    enabled: !!id && !!startDate && !!endDate,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
}

export function useMeetingRoomBookings(id: string, filters: any = {}) {
  return useQuery({
    queryKey: meetingRoomKeys.bookings(id, filters),
    queryFn: async () => {
      const response = await server.api['meeting-rooms']({ id }).bookings.get({
        query: filters
      });

      if (response.error) {
        throw new Error('Failed to fetch room bookings');
      }

      return response.data;
    },
    enabled: !!id,
    staleTime: 2 * 60 * 1000,
  });
}

export function useCreateMeetingRoom() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateMeetingRoomInput) => {
      const response = await server.api['meeting-rooms'].post(data);

      if (response.error) {
        throw new Error('Failed to create meeting room');
      }

      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: meetingRoomKeys.lists() });
      toast.success('Meeting room created successfully');
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

export function useUpdateMeetingRoom() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<CreateMeetingRoomInput> }) => {
      const response = await server.api['meeting-rooms']({ id }).put(data);

      if (response.error) {
        throw new Error('Failed to update meeting room');
      }

      return response.data;
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: meetingRoomKeys.lists() });
      queryClient.invalidateQueries({ queryKey: meetingRoomKeys.detail(variables.id) });
      toast.success('Meeting room updated successfully');
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

export function useDeleteMeetingRoom() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await server.api['meeting-rooms']({ id }).delete();

      if (response.error) {
        throw new Error('Failed to delete meeting room');
      }

      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: meetingRoomKeys.lists() });
      toast.success('Meeting room deleted successfully');
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

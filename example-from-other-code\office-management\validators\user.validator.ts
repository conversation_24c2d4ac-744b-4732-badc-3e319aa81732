import { body, query, param } from 'express-validator';
import mongoose from 'mongoose';

export class UserValidator {
  static updateProfile = [
    body('phone')
      .optional()
      .matches(/^\+?[\d\s\-\(\)]+$/)
      .withMessage('Phone number format is invalid'),
    
    body('officeLocation.building')
      .optional()
      .trim()
      .isLength({ max: 100 })
      .withMessage('Building name must not exceed 100 characters'),
    
    body('officeLocation.floor')
      .optional()
      .trim()
      .isLength({ min: 1, max: 50 })
      .withMessage('Floor must be between 1 and 50 characters'),
    
    body('officeLocation.desk')
      .optional()
      .trim()
      .isLength({ max: 50 })
      .withMessage('Desk must not exceed 50 characters'),
    
    body('workSchedule.workingDays')
      .optional()
      .isArray()
      .withMessage('Working days must be an array')
      .custom((value) => {
        if (value && value.length > 0) {
          const validDays = value.every((day: number) => Number.isInteger(day) && day >= 0 && day <= 6);
          if (!validDays) {
            throw new Error('Working days must be integers between 0 (Sunday) and 6 (Saturday)');
          }
        }
        return true;
      }),
    
    body('workSchedule.startTime')
      .optional()
      .matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)
      .withMessage('Start time must be in HH:MM format'),
    
    body('workSchedule.endTime')
      .optional()
      .matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)
      .withMessage('End time must be in HH:MM format')
      .custom((value, { req }) => {
        if (value && req.body.workSchedule?.startTime) {
          const startTime = req.body.workSchedule.startTime;
          const [startHour, startMin] = startTime.split(':').map(Number);
          const [endHour, endMin] = value.split(':').map(Number);

          const startMinutes = startHour * 60 + startMin;
          const endMinutes = endHour * 60 + endMin;

          if (endMinutes <= startMinutes) {
            throw new Error('End time must be after start time');
          }
        }
        return true;
      }),
    
    body('workSchedule.timezone')
      .optional()
      .isLength({ min: 1 })
      .withMessage('Timezone is required'),
    
    body('preferences.defaultMeetingDuration')
      .optional()
      .isInt({ min: 30, max: 480 })
      .withMessage('Default meeting duration must be between 30 and 480 minutes'),
    
    body('preferences.notificationSettings.email')
      .optional()
      .isBoolean()
      .withMessage('Email notification setting must be a boolean'),
    
    body('preferences.notificationSettings.slack')
      .optional()
      .isBoolean()
      .withMessage('Slack notification setting must be a boolean'),
    
    body('preferences.notificationSettings.whatsapp')
      .optional()
      .isBoolean()
      .withMessage('WhatsApp notification setting must be a boolean'),
    
    body('preferences.favoriteRooms')
      .optional()
      .isArray()
      .withMessage('Favorite rooms must be an array')
      .custom((value) => {
        if (value && value.length > 0) {
          const invalidIds = value.filter((id: string) => !mongoose.Types.ObjectId.isValid(id));
          if (invalidIds.length > 0) {
            throw new Error('All favorite room IDs must be valid MongoDB ObjectIds');
          }
        }
        return true;
      }),
    
    body('emergencyContact.name')
      .optional()
      .trim()
      .isLength({ max: 100 })
      .withMessage('Emergency contact name must not exceed 100 characters'),
    
    body('emergencyContact.phone')
      .optional()
      .matches(/^\+?[\d\s\-\(\)]+$/)
      .withMessage('Emergency contact phone format is invalid'),
    
    body('emergencyContact.relationship')
      .optional()
      .trim()
      .isLength({ max: 50 })
      .withMessage('Emergency contact relationship must not exceed 50 characters'),
    
    body('profilePicture')
      .optional()
      .isURL()
      .withMessage('Profile picture must be a valid URL'),
    
    body('slackUserId')
      .optional()
      .trim()
      .isLength({ min: 1 })
      .withMessage('Slack user ID cannot be empty')
  ];

  static createUser = [
    body('employeeId')
      .trim()
      .isLength({ min: 1, max: 20 })
      .withMessage('Employee ID must be between 1 and 20 characters')
      .isAlphanumeric()
      .withMessage('Employee ID must contain only letters and numbers'),
    
    body('firstName')
      .trim()
      .isLength({ min: 1, max: 50 })
      .withMessage('First name must be between 1 and 50 characters')
      .matches(/^[a-zA-ZÀ-ÿ\s]+$/)
      .withMessage('First name must contain only letters and spaces'),
    
    body('lastName')
      .trim()
      .isLength({ min: 1, max: 50 })
      .withMessage('Last name must be between 1 and 50 characters')
      .matches(/^[a-zA-ZÀ-ÿ\s]+$/)
      .withMessage('Last name must contain only letters and spaces'),
    
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Must be a valid email address')
      .custom((value) => {
        // Validate @onecarnow domain
        if (!value.endsWith('@onecarnow.com')) {
          throw new Error('Email must be from @onecarnow.com domain');
        }
        return true;
      }),
    
    body('phone')
      .optional()
      .matches(/^\+?[\d\s\-\(\)]+$/)
      .withMessage('Phone number format is invalid'),
    
    body('department')
      .trim()
      .isLength({ min: 1, max: 100 })
      .withMessage('Department must be between 1 and 100 characters'),
    
    body('position')
      .trim()
      .isLength({ min: 1, max: 100 })
      .withMessage('Position must be between 1 and 100 characters'),
    
    body('manager')
      .optional()
      .isMongoId()
      .withMessage('Manager must be a valid MongoDB ObjectId'),
    
    body('officeLocation.floor')
      .trim()
      .isLength({ min: 1, max: 50 })
      .withMessage('Floor must be between 1 and 50 characters'),
    
    body('permissions.canBookMeetingRooms')
      .optional()
      .isBoolean()
      .withMessage('Can book meeting rooms must be a boolean'),
    
    body('permissions.canBookDesks')
      .optional()
      .isBoolean()
      .withMessage('Can book desks must be a boolean'),
    
    body('permissions.canManageGuests')
      .optional()
      .isBoolean()
      .withMessage('Can manage guests must be a boolean'),
    
    body('permissions.canViewReports')
      .optional()
      .isBoolean()
      .withMessage('Can view reports must be a boolean'),
    
    body('permissions.isAdmin')
      .optional()
      .isBoolean()
      .withMessage('Is admin must be a boolean'),
    
    body('permissions.maxBookingDuration')
      .optional()
      .isInt({ min: 1, max: 24 })
      .withMessage('Max booking duration must be between 1 and 24 hours'),
    
    body('permissions.maxAdvanceBookingDays')
      .optional()
      .isInt({ min: 1, max: 90 })
      .withMessage('Max advance booking days must be between 1 and 90')
  ];

  static updateUser = [
    param('id')
      .isMongoId()
      .withMessage('User ID must be a valid MongoDB ObjectId'),
    
    body('employeeId')
      .optional()
      .trim()
      .isLength({ min: 1, max: 20 })
      .withMessage('Employee ID must be between 1 and 20 characters')
      .isAlphanumeric()
      .withMessage('Employee ID must contain only letters and numbers'),
    
    body('firstName')
      .optional()
      .trim()
      .isLength({ min: 1, max: 50 })
      .withMessage('First name must be between 1 and 50 characters')
      .matches(/^[a-zA-ZÀ-ÿ\s]+$/)
      .withMessage('First name must contain only letters and spaces'),
    
    body('lastName')
      .optional()
      .trim()
      .isLength({ min: 1, max: 50 })
      .withMessage('Last name must be between 1 and 50 characters')
      .matches(/^[a-zA-ZÀ-ÿ\s]+$/)
      .withMessage('Last name must contain only letters and spaces'),
    
    body('email')
      .optional()
      .isEmail()
      .normalizeEmail()
      .withMessage('Must be a valid email address')
      .custom((value) => {
        if (value && !value.endsWith('@onecarnow.com')) {
          throw new Error('Email must be from @onecarnow.com domain');
        }
        return true;
      }),
    
    body('department')
      .optional()
      .trim()
      .isLength({ min: 1, max: 100 })
      .withMessage('Department must be between 1 and 100 characters'),
    
    body('position')
      .optional()
      .trim()
      .isLength({ min: 1, max: 100 })
      .withMessage('Position must be between 1 and 100 characters'),
    
    body('manager')
      .optional()
      .isMongoId()
      .withMessage('Manager must be a valid MongoDB ObjectId')
  ];

  static getUsers = [
    query('department')
      .optional()
      .trim()
      .isLength({ min: 1 })
      .withMessage('Department filter cannot be empty'),
    
    query('isActive')
      .optional()
      .isBoolean()
      .withMessage('Is active filter must be a boolean'),
    
    query('floor')
      .optional()
      .trim()
      .isLength({ min: 1 })
      .withMessage('Floor filter cannot be empty'),
    
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100'),
    
    query('search')
      .optional()
      .trim()
      .isLength({ min: 1 })
      .withMessage('Search term cannot be empty')
  ];

  static getUserById = [
    param('id')
      .isMongoId()
      .withMessage('User ID must be a valid MongoDB ObjectId')
  ];

  static searchUsers = [
    query('q')
      .trim()
      .isLength({ min: 1 })
      .withMessage('Search query is required and cannot be empty'),
    
    query('department')
      .optional()
      .trim()
      .isLength({ min: 1 })
      .withMessage('Department filter cannot be empty'),
    
    query('limit')
      .optional()
      .isInt({ min: 1, max: 50 })
      .withMessage('Limit must be between 1 and 50')
  ];

  static getUserBookings = [
    param('id')
      .isMongoId()
      .withMessage('User ID must be a valid MongoDB ObjectId'),
    
    query('startDate')
      .optional()
      .isISO8601()
      .withMessage('Start date must be a valid ISO 8601 date'),
    
    query('endDate')
      .optional()
      .isISO8601()
      .withMessage('End date must be a valid ISO 8601 date'),
    
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100')
  ];

  static getUserGuests = [
    param('id')
      .isMongoId()
      .withMessage('User ID must be a valid MongoDB ObjectId'),
    
    query('startDate')
      .optional()
      .isISO8601()
      .withMessage('Start date must be a valid ISO 8601 date'),
    
    query('endDate')
      .optional()
      .isISO8601()
      .withMessage('End date must be a valid ISO 8601 date'),
    
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100')
  ];

  static updatePermissions = [
    param('id')
      .isMongoId()
      .withMessage('User ID must be a valid MongoDB ObjectId'),
    
    body('canBookMeetingRooms')
      .optional()
      .isBoolean()
      .withMessage('Can book meeting rooms must be a boolean'),
    
    body('canBookDesks')
      .optional()
      .isBoolean()
      .withMessage('Can book desks must be a boolean'),
    
    body('canManageGuests')
      .optional()
      .isBoolean()
      .withMessage('Can manage guests must be a boolean'),
    
    body('canViewReports')
      .optional()
      .isBoolean()
      .withMessage('Can view reports must be a boolean'),
    
    body('isAdmin')
      .optional()
      .isBoolean()
      .withMessage('Is admin must be a boolean'),
    
    body('maxBookingDuration')
      .optional()
      .isInt({ min: 1, max: 24 })
      .withMessage('Max booking duration must be between 1 and 24 hours'),
    
    body('maxAdvanceBookingDays')
      .optional()
      .isInt({ min: 1, max: 90 })
      .withMessage('Max advance booking days must be between 1 and 90')
  ];
}

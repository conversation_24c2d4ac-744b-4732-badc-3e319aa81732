import { WebClient } from '@slack/web-api';
import { NotificationData, NotificationResult } from './notification.service';

export class SlackService {
  private client: WebClient;

  constructor() {
    this.client = new WebClient(process.env.SLACK_BOT_TOKEN);
  }

  async sendBookingConfirmation(data: NotificationData): Promise<NotificationResult> {
    const { booking, user, meetingRoom } = data;
    
    if (!booking || !user || !meetingRoom || !user.slackUserId) {
      throw new Error('Missing required data for Slack booking confirmation');
    }

    const blocks = [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: '✅ Booking Confirmed'
        }
      },
      {
        type: 'section',
        fields: [
          {
            type: 'mrkdwn',
            text: `*Meeting:* ${booking.title}`
          },
          {
            type: 'mrkdwn',
            text: `*Room:* ${meetingRoom.name}`
          },
          {
            type: 'mrkdwn',
            text: `*Date:* ${new Date(booking.startDate).toLocaleDateString()}`
          },
          {
            type: 'mrkdwn',
            text: `*Time:* ${new Date(booking.startDate).toLocaleTimeString()} - ${new Date(booking.endDate).toLocaleTimeString()}`
          }
        ]
      }
    ];

    if (booking.description) {
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Description:* ${booking.description}`
        }
      });
    }

    try {
      const result = await this.client.chat.postMessage({
        channel: user.slackUserId,
        text: `Booking confirmed for ${meetingRoom.name}`,
        blocks
      });

      return {
        success: true,
        messageId: result.ts,
        sentAt: new Date()
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        sentAt: new Date()
      };
    }
  }

  async sendBookingReminder(data: NotificationData): Promise<NotificationResult> {
    const { booking, user, meetingRoom } = data;
    
    if (!booking || !user || !meetingRoom || !user.slackUserId) {
      throw new Error('Missing required data for Slack booking reminder');
    }

    const blocks = [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: '⏰ Meeting Reminder'
        }
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `Your meeting *${booking.title}* starts soon!`
        }
      },
      {
        type: 'section',
        fields: [
          {
            type: 'mrkdwn',
            text: `*Room:* ${meetingRoom.name}`
          },
          {
            type: 'mrkdwn',
            text: `*Location:* ${meetingRoom.location.floor}, ${meetingRoom.location.room}`
          },
          {
            type: 'mrkdwn',
            text: `*Time:* ${new Date(booking.startDate).toLocaleTimeString()} - ${new Date(booking.endDate).toLocaleTimeString()}`
          }
        ]
      }
    ];

    try {
      const result = await this.client.chat.postMessage({
        channel: user.slackUserId,
        text: `Meeting reminder: ${booking.title}`,
        blocks
      });

      return {
        success: true,
        messageId: result.ts,
        sentAt: new Date()
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        sentAt: new Date()
      };
    }
  }

  async sendBookingCancellation(data: NotificationData): Promise<NotificationResult> {
    const { booking, user, meetingRoom } = data;
    
    if (!booking || !user || !meetingRoom || !user.slackUserId) {
      throw new Error('Missing required data for Slack booking cancellation');
    }

    const blocks = [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: '❌ Booking Cancelled'
        }
      },
      {
        type: 'section',
        fields: [
          {
            type: 'mrkdwn',
            text: `*Meeting:* ${booking.title}`
          },
          {
            type: 'mrkdwn',
            text: `*Room:* ${meetingRoom.name}`
          },
          {
            type: 'mrkdwn',
            text: `*Date:* ${new Date(booking.startDate).toLocaleDateString()}`
          },
          {
            type: 'mrkdwn',
            text: `*Time:* ${new Date(booking.startDate).toLocaleTimeString()} - ${new Date(booking.endDate).toLocaleTimeString()}`
          }
        ]
      }
    ];

    if (booking.cancellationReason) {
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Reason:* ${booking.cancellationReason}`
        }
      });
    }

    try {
      const result = await this.client.chat.postMessage({
        channel: user.slackUserId,
        text: `Booking cancelled: ${booking.title}`,
        blocks
      });

      return {
        success: true,
        messageId: result.ts,
        sentAt: new Date()
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        sentAt: new Date()
      };
    }
  }

  async notifyHostOfGuestArrival(data: NotificationData): Promise<NotificationResult> {
    const { guest, user } = data;
    
    if (!guest || !user || !user.slackUserId) {
      throw new Error('Missing required data for Slack guest arrival notification');
    }

    const blocks = [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: '👋 Guest Arrival'
        }
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `Your guest *${guest.name}* has arrived and is waiting at reception.`
        }
      },
      {
        type: 'section',
        fields: [
          {
            type: 'mrkdwn',
            text: `*Guest:* ${guest.name}`
          },
          {
            type: 'mrkdwn',
            text: `*Purpose:* ${guest.purpose}`
          }
        ]
      }
    ];

    if (guest.company) {
      blocks[2].fields.push({
        type: 'mrkdwn',
        text: `*Company:* ${guest.company}`
      });
    }

    blocks.push({
      type: 'actions',
      elements: [
        {
          type: 'button',
          text: {
            type: 'plain_text',
            text: 'I\'m on my way'
          },
          style: 'primary',
          action_id: 'guest_host_coming'
        }
      ]
    });

    try {
      const result = await this.client.chat.postMessage({
        channel: user.slackUserId,
        text: `Your guest ${guest.name} has arrived`,
        blocks
      });

      return {
        success: true,
        messageId: result.ts,
        sentAt: new Date()
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        sentAt: new Date()
      };
    }
  }

  async sendCustomMessage(data: NotificationData): Promise<NotificationResult> {
    const { user, customMessage } = data;
    
    if (!user || !customMessage || !user.slackUserId) {
      throw new Error('Missing required data for Slack custom message');
    }

    try {
      const result = await this.client.chat.postMessage({
        channel: user.slackUserId,
        text: customMessage,
        blocks: [
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: customMessage
            }
          }
        ]
      });

      return {
        success: true,
        messageId: result.ts,
        sentAt: new Date()
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        sentAt: new Date()
      };
    }
  }

  // Utility method to send messages to channels (for admin notifications)
  async sendChannelMessage(channel: string, message: string, blocks?: any[]): Promise<NotificationResult> {
    try {
      const result = await this.client.chat.postMessage({
        channel,
        text: message,
        blocks
      });

      return {
        success: true,
        messageId: result.ts,
        sentAt: new Date()
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        sentAt: new Date()
      };
    }
  }

  // Method to update user's Slack status (for meeting rooms)
  async updateUserStatus(userId: string, status: string, emoji: string, expiration?: number): Promise<NotificationResult> {
    try {
      await this.client.users.profile.set({
        user: userId,
        profile: {
          status_text: status,
          status_emoji: emoji,
          status_expiration: expiration || 0
        }
      });

      return {
        success: true,
        sentAt: new Date()
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        sentAt: new Date()
      };
    }
  }
}

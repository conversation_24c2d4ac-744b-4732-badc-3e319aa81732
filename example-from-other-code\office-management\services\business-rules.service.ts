import { BookingModel } from '../models/booking.model';
import { MeetingRoomModel } from '../models/meeting-room.model';
import { OfficeUserModel } from '../models/user.model';
import { GuestModel } from '../models/guest.model';

export interface BusinessRuleResult {
  isValid: boolean;
  message?: string;
  details?: any;
}

export class BusinessRulesService {
  // Check if user can make a booking based on their permissions
  async canUserMakeBooking(userId: string, resourceType: 'meeting-room' | 'desk', duration: number): Promise<BusinessRuleResult> {
    try {
      const user = await OfficeUserModel.findById(userId);
      if (!user) {
        return { isValid: false, message: 'User not found' };
      }

      if (!user.isActive) {
        return { isValid: false, message: 'User account is not active' };
      }

      // Check resource-specific permissions
      if (resourceType === 'meeting-room' && !user.permissions.canBookMeetingRooms) {
        return { isValid: false, message: 'User does not have permission to book meeting rooms' };
      }

      if (resourceType === 'desk' && !user.permissions.canBookDesks) {
        return { isValid: false, message: 'User does not have permission to book desks' };
      }

      // Check duration limits
      const durationHours = duration / (1000 * 60 * 60);
      if (durationHours > user.permissions.maxBookingDuration) {
        return { 
          isValid: false, 
          message: `Booking duration exceeds maximum allowed (${user.permissions.maxBookingDuration} hours)` 
        };
      }

      return { isValid: true };
    } catch (error) {
      return { isValid: false, message: 'Error checking user permissions' };
    }
  }

  // Check if booking is within advance booking limits
  async checkAdvanceBookingLimits(userId: string, startDate: Date): Promise<BusinessRuleResult> {
    try {
      const user = await OfficeUserModel.findById(userId);
      if (!user) {
        return { isValid: false, message: 'User not found' };
      }

      const now = new Date();
      const daysDiff = Math.ceil((startDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

      if (daysDiff > user.permissions.maxAdvanceBookingDays) {
        return { 
          isValid: false, 
          message: `Booking is too far in advance. Maximum allowed: ${user.permissions.maxAdvanceBookingDays} days` 
        };
      }

      return { isValid: true };
    } catch (error) {
      return { isValid: false, message: 'Error checking advance booking limits' };
    }
  }

  // Check if booking is within business hours
  async checkBusinessHours(resourceId: string, resourceType: 'meeting-room' | 'desk', startDate: Date, endDate: Date): Promise<BusinessRuleResult> {
    try {
      if (resourceType === 'meeting-room') {
        const room = await MeetingRoomModel.findById(resourceId);
        if (!room) {
          return { isValid: false, message: 'Meeting room not found' };
        }

        const startTime = startDate.toTimeString().substring(0, 5); // HH:MM format
        const endTime = endDate.toTimeString().substring(0, 5);
        const dayOfWeek = startDate.getDay();

        // Check if booking is on a working day
        if (!room.availability.workingDays.includes(dayOfWeek)) {
          return { isValid: false, message: 'Booking is not on a working day for this room' };
        }

        // Check if booking is within business hours
        const businessStart = room.availability.businessHours.start;
        const businessEnd = room.availability.businessHours.end;

        if (startTime < businessStart || endTime > businessEnd) {
          return { 
            isValid: false, 
            message: `Booking must be within business hours (${businessStart} - ${businessEnd})` 
          };
        }

        // Check minimum notice requirement
        const now = new Date();
        const hoursNotice = (startDate.getTime() - now.getTime()) / (1000 * 60 * 60);
        
        if (hoursNotice < room.bookingRules.minNoticeHours) {
          return { 
            isValid: false, 
            message: `Minimum ${room.bookingRules.minNoticeHours} hours notice required` 
          };
        }

        // Check booking duration limits
        const duration = (endDate.getTime() - startDate.getTime()) / (1000 * 60);
        
        if (duration < room.bookingRules.minBookingDuration) {
          return { 
            isValid: false, 
            message: `Minimum booking duration is ${room.bookingRules.minBookingDuration} minutes` 
          };
        }

        if (duration > room.bookingRules.maxBookingDuration) {
          return { 
            isValid: false, 
            message: `Maximum booking duration is ${room.bookingRules.maxBookingDuration} minutes` 
          };
        }
      }

      return { isValid: true };
    } catch (error) {
      return { isValid: false, message: 'Error checking business hours' };
    }
  }

  // Check for booking conflicts
  async checkBookingConflicts(resourceId: string, resourceType: 'meeting-room' | 'desk', startDate: Date, endDate: Date, excludeBookingId?: string): Promise<BusinessRuleResult> {
    try {
      const query: any = {
        resourceId,
        resourceType,
        status: { $in: ['confirmed', 'completed'] },
        $or: [
          {
            startDate: { $lt: endDate },
            endDate: { $gt: startDate }
          }
        ]
      };

      if (excludeBookingId) {
        query._id = { $ne: excludeBookingId };
      }

      const conflictingBookings = await BookingModel.find(query);

      if (conflictingBookings.length > 0) {
        return { 
          isValid: false, 
          message: 'Time slot conflicts with existing booking',
          details: conflictingBookings.map(booking => ({
            id: booking._id,
            title: booking.title,
            startDate: booking.startDate,
            endDate: booking.endDate
          }))
        };
      }

      return { isValid: true };
    } catch (error) {
      return { isValid: false, message: 'Error checking booking conflicts' };
    }
  }

  // Check user's concurrent booking limits
  async checkConcurrentBookingLimits(userId: string, startDate: Date, endDate: Date): Promise<BusinessRuleResult> {
    try {
      // Check for overlapping bookings by the same user
      const overlappingBookings = await BookingModel.find({
        userId,
        status: { $in: ['confirmed', 'completed'] },
        $or: [
          {
            startDate: { $lt: endDate },
            endDate: { $gt: startDate }
          }
        ]
      });

      if (overlappingBookings.length > 0) {
        return { 
          isValid: false, 
          message: 'You already have a booking during this time',
          details: overlappingBookings.map(booking => ({
            id: booking._id,
            title: booking.title,
            startDate: booking.startDate,
            endDate: booking.endDate
          }))
        };
      }

      return { isValid: true };
    } catch (error) {
      return { isValid: false, message: 'Error checking concurrent booking limits' };
    }
  }

  // Validate guest visit rules
  async validateGuestVisit(guestData: any, hostUserId: string): Promise<BusinessRuleResult> {
    try {
      const host = await OfficeUserModel.findById(hostUserId);
      if (!host) {
        return { isValid: false, message: 'Host user not found' };
      }

      if (!host.isActive) {
        return { isValid: false, message: 'Host account is not active' };
      }

      if (!host.permissions.canManageGuests) {
        return { isValid: false, message: 'Host does not have permission to manage guests' };
      }

      // Check if visit date is in the future
      const visitDate = new Date(guestData.visitDate);
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      if (visitDate < today) {
        return { isValid: false, message: 'Visit date cannot be in the past' };
      }

      // Check if visit date is not too far in the future (90 days)
      const maxDate = new Date();
      maxDate.setDate(maxDate.getDate() + 90);
      if (visitDate > maxDate) {
        return { isValid: false, message: 'Visit date cannot be more than 90 days in the future' };
      }

      // Check if visit is on a working day for the host
      const dayOfWeek = visitDate.getDay();
      if (!host.workSchedule.workingDays.includes(dayOfWeek)) {
        return { isValid: false, message: 'Visit date is not on a working day for the host' };
      }

      // Check if visit time is within host's working hours
      if (guestData.visitTime?.start) {
        const visitStartTime = guestData.visitTime.start;
        const hostStartTime = host.workSchedule.startTime;
        const hostEndTime = host.workSchedule.endTime;

        if (visitStartTime < hostStartTime || visitStartTime > hostEndTime) {
          return { 
            isValid: false, 
            message: `Visit time must be within host's working hours (${hostStartTime} - ${hostEndTime})` 
          };
        }
      }

      // Check for duplicate guests on the same day
      const existingGuests = await GuestModel.find({
        hostUserId,
        visitDate: {
          $gte: visitDate,
          $lt: new Date(visitDate.getTime() + 24 * 60 * 60 * 1000)
        },
        status: { $in: ['invited', 'confirmed', 'checked-in'] }
      });

      // Limit to 5 guests per host per day
      if (existingGuests.length >= 5) {
        return { isValid: false, message: 'Maximum 5 guests per host per day' };
      }

      return { isValid: true };
    } catch (error) {
      return { isValid: false, message: 'Error validating guest visit' };
    }
  }

  // Check email domain validation
  validateEmailDomain(email: string): BusinessRuleResult {
    if (!email.endsWith('@onecarnow.com')) {
      return { isValid: false, message: 'Email must be from @onecarnow.com domain' };
    }
    return { isValid: true };
  }

  // Check if user can cancel booking
  async canCancelBooking(bookingId: string, userId: string): Promise<BusinessRuleResult> {
    try {
      const booking = await BookingModel.findById(bookingId);
      if (!booking) {
        return { isValid: false, message: 'Booking not found' };
      }

      // Only the booking owner or admin can cancel
      if (booking.userId.toString() !== userId) {
        const user = await OfficeUserModel.findById(userId);
        if (!user || !user.permissions.isAdmin) {
          return { isValid: false, message: 'Only booking owner or admin can cancel bookings' };
        }
      }

      // Cannot cancel past bookings
      if (booking.endDate < new Date()) {
        return { isValid: false, message: 'Cannot cancel past bookings' };
      }

      // Cannot cancel already cancelled bookings
      if (booking.status === 'cancelled') {
        return { isValid: false, message: 'Booking is already cancelled' };
      }

      return { isValid: true };
    } catch (error) {
      return { isValid: false, message: 'Error checking cancellation permissions' };
    }
  }

  // Comprehensive booking validation
  async validateBooking(bookingData: any, userId: string): Promise<BusinessRuleResult> {
    const startDate = new Date(bookingData.startDate);
    const endDate = new Date(bookingData.endDate);
    const duration = endDate.getTime() - startDate.getTime();

    // Check user permissions
    const userPermissionCheck = await this.canUserMakeBooking(userId, bookingData.resourceType, duration);
    if (!userPermissionCheck.isValid) {
      return userPermissionCheck;
    }

    // Check advance booking limits
    const advanceBookingCheck = await this.checkAdvanceBookingLimits(userId, startDate);
    if (!advanceBookingCheck.isValid) {
      return advanceBookingCheck;
    }

    // Check business hours
    const businessHoursCheck = await this.checkBusinessHours(
      bookingData.resourceId, 
      bookingData.resourceType, 
      startDate, 
      endDate
    );
    if (!businessHoursCheck.isValid) {
      return businessHoursCheck;
    }

    // Check for conflicts
    const conflictCheck = await this.checkBookingConflicts(
      bookingData.resourceId, 
      bookingData.resourceType, 
      startDate, 
      endDate
    );
    if (!conflictCheck.isValid) {
      return conflictCheck;
    }

    // Check concurrent booking limits
    const concurrentCheck = await this.checkConcurrentBookingLimits(userId, startDate, endDate);
    if (!concurrentCheck.isValid) {
      return concurrentCheck;
    }

    return { isValid: true };
  }
}

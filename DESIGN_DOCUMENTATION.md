# OCN House - Design Documentation

## Project Overview

**OCN House** is a comprehensive office management platform for OneCarNow, designed to streamline workspace reservations, guest management, and office operations. The platform serves as a central hub for employees to book meeting rooms, reserve desks, manage guest invitations, and handle daily office logistics.

### Target Users
- **Employees**: Book meeting rooms and desks, manage their reservations
- **Administrators**: Manage spaces, view analytics, configure system settings
- **Receptionists**: Handle guest check-ins/check-outs, manage daily operations
- **Guests**: External visitors with guided check-in experience

## Technical Foundation

### Tech Stack
- **Frontend**: Next.js 15.3.4 with TypeScript and React 19
- **Styling**: Tailwind CSS 4 with Shadcn/ui components
- **Backend**: Elysia.js with modular architecture
- **Database**: MongoDB with Prisma ORM
- **State Management**: React Query + Zustand
- **Authentication**: Better Auth with Google SSO
- **Calendar**: React Big Calendar with <PERSON><PERSON> for date handling
- **Notifications**: <PERSON><PERSON> (Nodemailer) + Slack integration
- **Package Manager**: Bun

### Design System Foundation
- **Component Library**: Shadcn/ui (New York style)
- **Icon Library**: Lucide React
- **Typography**: Geist Sans (primary), Geist Mono (code)
- **Base Color**: Neutral with CSS variables for theming
- **Border Radius**: 0.625rem (10px) base radius
- **Language**: Spanish UI text preferred

## Current Design Language

### Color Palette
- **Primary Brand**: Purple (#7C3AED - purple-600)
  - Primary buttons: `bg-purple-600 hover:bg-purple-700`
  - Accent elements and CTAs
- **Background**: Clean white/neutral backgrounds
- **Cards**: White with subtle shadows and rounded corners (rounded-xl)
- **Text**: 
  - Primary: Dark gray/black for headings
  - Secondary: Muted gray for descriptions
- **Status Colors**:
  - Confirmed: Blue (bg-blue-100 text-blue-800)
  - Completed: Green (bg-green-100 text-green-800)
  - Cancelled: Red (bg-red-100 text-red-800)
  - No Show: Gray (bg-gray-100 text-gray-800)

### Typography Hierarchy
- **Page Titles**: `text-3xl font-bold tracking-tight`
- **Section Headers**: `text-xl font-semibold`
- **Card Titles**: `text-lg font-medium`
- **Body Text**: `text-sm` or `text-base`
- **Descriptions**: `text-muted-foreground`

### Layout Patterns
- **Dashboard Layout**: Fixed sidebar (lg:pl-72) with main content area
- **Content Spacing**: `gap-6` for major sections, `gap-4` for related items
- **Card Padding**: `py-6 px-6` standard card padding
- **Page Padding**: `p-6` for main content areas

## Core Features & User Flows

### 1. Authentication & Access
- **Login Page**: Root path with background image (public/background.avif)
- **Google SSO**: Better Auth integration with @onecarnow.com domain restriction
- **Role-based Access**: USER, ADMIN, RECEPTIONIST roles

### 2. Dashboard Overview
- **Main Dashboard**: `/dashboard` - Central hub with metrics and quick actions
- **Today's Bookings**: Real-time view of current day reservations
- **Quick Stats**: Cards showing booking counts, room utilization
- **Quick Actions**: Primary CTAs for common tasks (Nueva Reserva, Explorar Salas)

### 3. Booking Management
- **Meeting Rooms**: `/dashboard/meeting-rooms`
  - Grid/list view of available rooms
  - Detailed room pages with amenities, capacity, images
  - Availability checking and booking flow
- **Desks**: `/dashboard/desks`
  - Desk types: Regular, Standing, Executive
  - Location-based organization (building, floor, zone)
- **Booking Flow**: `/dashboard/bookings/new`
  - Calendar integration (React Big Calendar)
  - Resource selection and time slot picking
  - Guest invitation during booking

### 4. Guest Management
- **Guest Invitations**: `/dashboard/guests`
  - Create and manage external visitor invitations
  - Email/WhatsApp notifications
  - Check-in/check-out tracking
- **Reception Dashboard**: `/dashboard/reception`
  - Real-time guest status
  - Daily visitor management

### 5. Administration
- **Admin Panel**: `/dashboard/admin`
  - Resource management (rooms, desks)
  - User management and permissions
  - System configuration and analytics

## Data Models & Relationships

### Core Entities
1. **Users**: Employees with roles and departments
2. **Meeting Rooms**: Bookable spaces with capacity, amenities, equipment
3. **Desks**: Individual workstations with types and locations
4. **Bookings**: Reservations linking users to resources with time slots
5. **Guests**: External visitors with check-in/out tracking

### Key Relationships
- Users can have multiple bookings
- Bookings can include multiple guests
- Resources (rooms/desks) have availability rules
- Notifications track email/Slack delivery status

## UI Components & Patterns

### Navigation
- **Sidebar**: Fixed left navigation with role-based menu items
- **Breadcrumbs**: Context-aware navigation for deep pages
- **Mobile**: Collapsible sidebar with hamburger menu

### Data Display
- **Cards**: Primary container for grouped information
- **Tables**: Booking lists, guest management, admin views
- **Calendar**: React Big Calendar for scheduling interface
- **Status Badges**: Color-coded status indicators
- **Metrics Cards**: Dashboard statistics with icons

### Forms & Interactions
- **React Hook Form**: All form implementations
- **Validation**: Real-time validation with error states
- **Modals**: Booking creation, confirmation dialogs
- **Buttons**: Purple primary, outline secondary, destructive for cancellations

### Responsive Design
- **Desktop First**: Primary experience optimized for desktop use
- **Mobile Adaptation**: Responsive sidebar, stacked layouts
- **Tablet**: Intermediate breakpoints for optimal viewing

## Integration Points

### Slack Integration
- **Booking Confirmations**: Automatic notifications with action buttons
- **Guest Notifications**: Check-in alerts to relevant channels
- **Admin Alerts**: System notifications and approval requests

### Email System
- **Booking Confirmations**: Automated email confirmations
- **Guest Invitations**: Professional invitation emails
- **Reminders**: Pre-meeting and check-in reminders

### Calendar Integration
- **React Big Calendar**: Primary scheduling interface
- **Luxon**: Date/time handling and timezone support
- **Availability Checking**: Real-time conflict detection

## Design Considerations

### Accessibility
- **Focus States**: Visible focus indicators for keyboard navigation
- **Color Contrast**: WCAG compliant color combinations
- **Screen Readers**: Semantic HTML and ARIA labels
- **Keyboard Navigation**: Full keyboard accessibility

### Performance
- **React Query**: Efficient data fetching and caching
- **Image Optimization**: Next.js image optimization
- **Code Splitting**: Route-based code splitting
- **Lazy Loading**: Component and data lazy loading

### Internationalization
- **Spanish Primary**: UI text in Spanish
- **Date Formats**: Localized date/time formatting
- **Timezone Handling**: Mexico City timezone default

## Future Enhancements

### Planned Features
- **Mobile App**: Native mobile application
- **Advanced Analytics**: Detailed usage analytics and reporting
- **Resource Optimization**: AI-powered space utilization recommendations
- **Integration Expansion**: Calendar sync, HR systems integration

### Design Evolution
- **Dark Mode**: Theme switching capability
- **Customization**: Tenant-specific branding options
- **Advanced Filtering**: Enhanced search and filter capabilities
- **Real-time Updates**: WebSocket integration for live updates

## Development Guidelines

### Component Structure
- **Atomic Design**: Atoms, molecules, organisms pattern
- **Reusability**: Shared components in `/components/ui`
- **Consistency**: Standardized props and styling patterns

### State Management
- **React Query**: Server state management
- **Zustand**: Client state for UI interactions
- **Context**: User session and authentication state

### API Design
- **RESTful**: Standard REST endpoints with consistent responses
- **Type Safety**: Full TypeScript integration
- **Error Handling**: Standardized error responses and user feedback

This documentation provides a comprehensive foundation for understanding the OCN House platform's design requirements, technical constraints, and user experience goals. Use this as a reference for creating cohesive, user-centered designs that align with the existing system architecture and business objectives.

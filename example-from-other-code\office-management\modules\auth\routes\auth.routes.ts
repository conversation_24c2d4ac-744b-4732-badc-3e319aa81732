import { Router } from 'express';
import { errorHandlerV2 } from '@/clean/errors/errorHandler';
import { verifyTokenOfficeManagement } from '../../../middlewares/verification-token';
import {
  getGoogleAuthUrl,
  googleCallback,
  authenticateWithGoogle,
  verifyToken,
  logout,
  getProfile,
} from '../controllers/auth.controller';

const authRouter = Router();

const authUrl = '/auth';

// Public routes
authRouter.get(authUrl + '/google/url', errorHandlerV2(getGoogleAuthUrl));
authRouter.post(authUrl + '/google/callback', errorHandlerV2(googleCallback));
authRouter.post(authUrl + '/google/login', errorHandlerV2(authenticateWithGoogle));

// Protected routes
authRouter.post(authUrl + '/verify', verifyTokenOfficeManagement, errorHandlerV2(verifyToken));
authRouter.post(authUrl + '/logout', verifyTokenOfficeManagement, errorHandlerV2(logout));
authRouter.get(authUrl + '/profile', verifyTokenOfficeManagement, errorHandlerV2(getProfile));

export default authRouter;

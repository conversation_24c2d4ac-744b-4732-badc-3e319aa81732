import React from 'react';
import { Sidebar } from '@/components/dashboard/sidebar';
import { UserProvider } from '@/context/user-provider';
import { getSession } from '@/actions/getSession';

export default async function DashboardLayout({ children }: { children: React.ReactNode }) {

  const session = await getSession();

  return (
    <>
      <UserProvider user={session.user} session={session.session}>

        <div className="min-h-screen bg-background">
          <Sidebar />

          {/* Main content */}
          <div className="lg:pl-72">
            <main className="min-h-screen">
              {children}
            </main>
          </div>
        </div>
      </UserProvider>
    </>
  );
}

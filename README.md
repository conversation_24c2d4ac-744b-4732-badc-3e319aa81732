# 🏢 OCN House - Sistema de Gestión de Oficinas

Sistema integral para la gestión de espacios de oficina de OneCarNow, incluyendo reservas de salas de reunión, escritorios, gestión de huéspedes e invitaciones con confirmaciones por Slack.

## 🚀 Características Principales

- ✅ **Gestión de Salas de Reunión** - CRUD completo con reglas de reserva
- ✅ **Gestión de Escritorios** - Tipos: Regular, Standing, Ejecutivo
- ✅ **Sistema de Reservas** - Calendario integrado con React Big Calendar
- ✅ **Gestión de Huéspedes** - Invitaciones con check-in/out
- ✅ **Dashboard de Recepción** - Gestión en tiempo real
- ✅ **Panel de Administración** - Configuración y estadísticas
- ✅ **Notificaciones** - Email y Slack automáticos
- 🔄 **Autenticación** - Better Auth con Google SSO (pendiente integración)

## 🛠️ Stack Tecnológico

- **Frontend**: Next.js 15.3.4 + TypeScript + Tailwind CSS
- **Backend**: Elysia.js con arquitectura modular
- **Base de Datos**: MongoDB + Prisma ORM
- **Estado**: React Query + Zustand
- **UI**: Shadcn/ui components
- **Calendario**: React Big Calendar + Luxon
- **Gestión de Paquetes**: Bun

## 📦 Instalación

```bash
# Instalar dependencias
bun install

# Configurar variables de entorno
cp .env.example .env.local
# Editar .env.local con tus configuraciones

# Configurar base de datos
bunx prisma generate
bunx prisma db push

# Ejecutar en desarrollo
bun dev
```

## 🔧 Variables de Entorno

```env
# Database
DATABASE_URL="mongodb://localhost:27017/ocn-house"

# Email Service
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# Slack Integration
SLACK_BOT_TOKEN="xoxb-your-bot-token"
SLACK_SIGNING_SECRET="your-signing-secret"
SLACK_CHANNEL_ID="your-channel-id"

# Better Auth
AUTH_SECRET="your-auth-secret"
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
```

## 📱 Páginas Implementadas

### Dashboard
- `/dashboard` - Overview con métricas
- `/dashboard/bookings` - Gestión de reservas
- `/dashboard/bookings/new` - Nueva reserva con calendario
- `/dashboard/meeting-rooms` - Explorar salas
- `/dashboard/meeting-rooms/[id]` - Detalle de sala
- `/dashboard/guests` - Gestión de huéspedes
- `/dashboard/reception` - Dashboard de recepción

### Administración
- `/dashboard/admin` - Panel principal
- `/dashboard/admin/meeting-rooms` - Gestión de salas
- `/dashboard/admin/desks` - Gestión de escritorios

## 🔌 API Endpoints

Consulta `api-endpoints.http` para ejemplos completos de todos los endpoints.

## 📊 Documentación

- **Documentación completa**: `DOCUMENTATION.md`
- **API Reference**: `api-endpoints.http`
- **Esquemas de BD**: `prisma/schema.prisma`

## 🚀 Comandos Útiles

```bash
# Desarrollo
bun dev                    # Servidor de desarrollo
bun build                  # Build de producción

# Base de datos
bunx prisma studio         # Explorador de BD
bunx prisma db push        # Aplicar cambios al schema
```

## 📋 Próximos Pasos

- [ ] Integrar Better Auth con Google OAuth
- [ ] Implementar formularios de edición
- [ ] Crear páginas de detalle faltantes
- [ ] Tests unitarios e integración

---

**Desarrollado para OneCarNow** 🚗

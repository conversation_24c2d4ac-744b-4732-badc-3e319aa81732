### Office Management - Meeting Rooms API Tests

@baseUrl = http://localhost:3000
@authToken = your-jwt-token-here

### Get all meeting rooms
GET {{baseUrl}}/api/office-management/meeting-rooms
Authorization: Bearer {{authToken}}

### Get meeting rooms with filters
GET {{baseUrl}}/api/office-management/meeting-rooms?capacity=10&floor=2&amenities=projector,whiteboard
Authorization: Bearer {{authToken}}

### Get available meeting rooms for specific time
GET {{baseUrl}}/api/office-management/meeting-rooms/available?startDate=2024-01-15T09:00:00.000Z&endDate=2024-01-15T10:00:00.000Z&capacity=8
Authorization: Bearer {{authToken}}

### Get specific meeting room
GET {{baseUrl}}/api/office-management/meeting-rooms/60f7b3b3b3b3b3b3b3b3b3b3
Authorization: Bearer {{authToken}}

### Create new meeting room (admin only)
POST {{baseUrl}}/api/office-management/meeting-rooms
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "name": "Conference Room A",
  "location": {
    "building": "Main Building",
    "floor": "2nd Floor",
    "room": "Room 201",
    "address": "123 Business St, City"
  },
  "capacity": 12,
  "amenities": ["projector", "whiteboard", "video_conference", "coffee_machine"],
  "equipment": ["laptop", "hdmi_cable", "markers"],
  "description": "Large conference room with video conferencing capabilities",
  "images": ["https://example.com/room1.jpg", "https://example.com/room2.jpg"],
  "bookingRules": {
    "minBookingDuration": 60,
    "maxBookingDuration": 480,
    "advanceBookingDays": 30,
    "minNoticeHours": 2
  },
  "availability": {
    "businessHours": {
      "start": "08:00",
      "end": "18:00"
    },
    "workingDays": [1, 2, 3, 4, 5],
    "timezone": "America/Mexico_City"
  }
}

### Update meeting room (admin only)
PUT {{baseUrl}}/api/office-management/meeting-rooms/60f7b3b3b3b3b3b3b3b3b3b3
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "capacity": 15,
  "amenities": ["projector", "whiteboard", "video_conference", "coffee_machine", "smart_tv"],
  "description": "Updated large conference room with smart TV"
}

### Deactivate meeting room (admin only)
PATCH {{baseUrl}}/api/office-management/meeting-rooms/60f7b3b3b3b3b3b3b3b3b3b3/deactivate
Authorization: Bearer {{authToken}}

### Activate meeting room (admin only)
PATCH {{baseUrl}}/api/office-management/meeting-rooms/60f7b3b3b3b3b3b3b3b3b3b3/activate
Authorization: Bearer {{authToken}}

### Get meeting room schedule
GET {{baseUrl}}/api/office-management/meeting-rooms/60f7b3b3b3b3b3b3b3b3b3b3/schedule?startDate=2024-01-15&endDate=2024-01-21
Authorization: Bearer {{authToken}}

### Get meeting room utilization stats
GET {{baseUrl}}/api/office-management/meeting-rooms/60f7b3b3b3b3b3b3b3b3b3b3/stats?period=month
Authorization: Bearer {{authToken}}

### Get all meeting rooms utilization
GET {{baseUrl}}/api/office-management/meeting-rooms/utilization?period=week&floor=2
Authorization: Bearer {{authToken}}

### Search meeting rooms
GET {{baseUrl}}/api/office-management/meeting-rooms/search?q=conference&capacity=10
Authorization: Bearer {{authToken}}

### Get meeting room amenities list
GET {{baseUrl}}/api/office-management/meeting-rooms/amenities
Authorization: Bearer {{authToken}}

### Get meeting room equipment list
GET {{baseUrl}}/api/office-management/meeting-rooms/equipment
Authorization: Bearer {{authToken}}

### Check room availability for recurring booking
POST {{baseUrl}}/api/office-management/meeting-rooms/check-recurring-availability
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "roomId": "60f7b3b3b3b3b3b3b3b3b3b3",
  "startTime": "09:00",
  "endTime": "10:00",
  "recurrence": {
    "type": "weekly",
    "daysOfWeek": [1, 3, 5],
    "startDate": "2024-01-15",
    "endDate": "2024-03-15"
  }
}

### Get room booking conflicts
GET {{baseUrl}}/api/office-management/meeting-rooms/60f7b3b3b3b3b3b3b3b3b3b3/conflicts?startDate=2024-01-15T09:00:00.000Z&endDate=2024-01-15T10:00:00.000Z
Authorization: Bearer {{authToken}}

### Add room to favorites
POST {{baseUrl}}/api/office-management/meeting-rooms/60f7b3b3b3b3b3b3b3b3b3b3/favorite
Authorization: Bearer {{authToken}}

### Remove room from favorites
DELETE {{baseUrl}}/api/office-management/meeting-rooms/60f7b3b3b3b3b3b3b3b3b3b3/favorite
Authorization: Bearer {{authToken}}

### Get user's favorite rooms
GET {{baseUrl}}/api/office-management/meeting-rooms/favorites
Authorization: Bearer {{authToken}}

### Upload room image (admin only)
POST {{baseUrl}}/api/office-management/meeting-rooms/60f7b3b3b3b3b3b3b3b3b3b3/images
Authorization: Bearer {{authToken}}
Content-Type: multipart/form-data

### Delete room image (admin only)
DELETE {{baseUrl}}/api/office-management/meeting-rooms/60f7b3b3b3b3b3b3b3b3b3b3/images/image-url-encoded
Authorization: Bearer {{authToken}}

### Get room maintenance schedule
GET {{baseUrl}}/api/office-management/meeting-rooms/60f7b3b3b3b3b3b3b3b3b3b3/maintenance
Authorization: Bearer {{authToken}}

### Schedule room maintenance (admin only)
POST {{baseUrl}}/api/office-management/meeting-rooms/60f7b3b3b3b3b3b3b3b3b3b3/maintenance
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "startDate": "2024-01-20T08:00:00.000Z",
  "endDate": "2024-01-20T12:00:00.000Z",
  "description": "HVAC maintenance",
  "maintenanceType": "hvac",
  "contactPerson": "Maintenance Team"
}

### Delete meeting room (admin only)
DELETE {{baseUrl}}/api/office-management/meeting-rooms/60f7b3b3b3b3b3b3b3b3b3b3
Authorization: Bearer {{authToken}}

### Export room data
GET {{baseUrl}}/api/office-management/meeting-rooms/export?format=csv
Authorization: Bearer {{authToken}}

### Get room QR code for quick booking
GET {{baseUrl}}/api/office-management/meeting-rooms/60f7b3b3b3b3b3b3b3b3b3b3/qr-code
Authorization: Bearer {{authToken}}

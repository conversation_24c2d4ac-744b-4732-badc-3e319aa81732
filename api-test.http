### API Test File for OneCarNow Office Management
### Base URL: http://localhost:4002/api

### Health Check
GET http://localhost:4002/api/health

### API Info
GET http://localhost:4002/api/

### ===== DESKS =====

### Get all desks
GET http://localhost:4002/api/desks
Content-Type: application/json

### Get desks with filters
GET http://localhost:4002/api/desks?page=1&limit=5&isActive=true&floor=2
Content-Type: application/json

### Create a new desk
POST http://localhost:4002/api/desks
Content-Type: application/json

{
  "name": "Desk A1",
  "location": {
    "building": "Main Building",
    "floor": "2",
    "zone": "North Wing",
    "address": "123 Main St"
  },
  "description": "Standing desk with monitor",
  "isActive": true,
  "equipment": ["Monitor", "Keyboard", "Mouse"],
  "images": ["desk1.jpg", "desk2.jpg"]
}

### Get desk by ID (replace with actual ID)
GET http://localhost:4002/api/desks/DESK_ID_HERE
Content-Type: application/json

### Update desk (replace with actual ID)
PUT http://localhost:4002/api/desks/DESK_ID_HERE
Content-Type: application/json

{
  "name": "Updated Desk A1",
  "description": "Updated standing desk with dual monitors"
}

### Check desk availability (replace with actual ID)
GET http://localhost:4002/api/desks/DESK_ID_HERE/availability?date=2024-01-15
Content-Type: application/json

### Delete desk (replace with actual ID)
DELETE http://localhost:4002/api/desks/DESK_ID_HERE
Content-Type: application/json

### ===== MEETING ROOMS =====

### Get all meeting rooms
GET http://localhost:4002/api/meeting-rooms
Content-Type: application/json

### Get meeting rooms with filters
GET http://localhost:4002/api/meeting-rooms?capacity=10&floor=3&amenities=Projector
Content-Type: application/json

### Create a new meeting room
POST http://localhost:4002/api/meeting-rooms
Content-Type: application/json

{
  "name": "Conference Room A",
  "location": {
    "building": "Main Building",
    "floor": "3",
    "room": "301A",
    "address": "123 Main St"
  },
  "capacity": 12,
  "amenities": ["Whiteboard", "Projector"],
  "equipment": ["TV", "Conference Phone"],
  "description": "Large conference room",
  "isActive": true,
  "images": ["room1.jpg"],
  "bookingRules": {
    "minBookingDuration": 30,
    "maxBookingDuration": 480,
    "advanceBookingDays": 30,
    "minNoticeHours": 2
  },
  "availability": {
    "businessHours": {
      "start": "09:00",
      "end": "18:00"
    },
    "workingDays": [1, 2, 3, 4, 5],
    "timezone": "America/Mexico_City"
  }
}

### Get meeting room by ID (replace with actual ID)
GET http://localhost:4002/api/meeting-rooms/ROOM_ID_HERE
Content-Type: application/json

### Update meeting room (replace with actual ID)
PUT http://localhost:4002/api/meeting-rooms/ROOM_ID_HERE
Content-Type: application/json

{
  "name": "Updated Conference Room A",
  "capacity": 15
}

### Check meeting room availability (replace with actual ID)
GET http://localhost:4002/api/meeting-rooms/ROOM_ID_HERE/availability?startDate=2024-01-15T10:00:00Z&endDate=2024-01-15T11:00:00Z
Content-Type: application/json

### Get meeting room bookings (replace with actual ID)
GET http://localhost:4002/api/meeting-rooms/ROOM_ID_HERE/bookings
Content-Type: application/json

### Delete meeting room (replace with actual ID)
DELETE http://localhost:4002/api/meeting-rooms/ROOM_ID_HERE
Content-Type: application/json

### ===== BOOKINGS =====

### Get all bookings
GET http://localhost:4002/api/bookings
Content-Type: application/json

### Get bookings with filters
GET http://localhost:4002/api/bookings?status=CONFIRMED&resourceType=MEETING_ROOM&startDate=2024-01-01
Content-Type: application/json
x-user-id: USER_ID_HERE

### Create a new booking
POST http://localhost:4002/api/bookings
Content-Type: application/json
x-user-id: USER_ID_HERE

{
  "resourceType": "MEETING_ROOM",
  "resourceId": "ROOM_ID_HERE",
  "title": "Team Meeting",
  "description": "Weekly team sync",
  "startDate": "2024-01-15T10:00:00Z",
  "endDate": "2024-01-15T11:00:00Z",
  "attendees": {
    "internal": ["user-id-1", "user-id-2"],
    "external": ["<EMAIL>"]
  }
}

### Get booking by ID (replace with actual ID)
GET http://localhost:4002/api/bookings/BOOKING_ID_HERE
Content-Type: application/json
x-user-id: USER_ID_HERE

### Update booking (replace with actual ID)
PUT http://localhost:4002/api/bookings/BOOKING_ID_HERE
Content-Type: application/json
x-user-id: USER_ID_HERE

{
  "title": "Updated Team Meeting",
  "description": "Updated weekly team sync"
}

### Cancel booking (replace with actual ID)
POST http://localhost:4002/api/bookings/BOOKING_ID_HERE/cancel
Content-Type: application/json
x-user-id: USER_ID_HERE

{
  "reason": "Meeting cancelled due to schedule conflict"
}

### Check in to booking (replace with actual ID)
POST http://localhost:4002/api/bookings/BOOKING_ID_HERE/check-in
Content-Type: application/json
x-user-id: USER_ID_HERE

### Check out from booking (replace with actual ID)
POST http://localhost:4002/api/bookings/BOOKING_ID_HERE/check-out
Content-Type: application/json
x-user-id: USER_ID_HERE

### Get booking statistics
GET http://localhost:4002/api/bookings/stats?startDate=2024-01-01&endDate=2024-01-31
Content-Type: application/json

### Get today's bookings
GET http://localhost:4002/api/bookings/today
Content-Type: application/json

### Get user bookings (replace with actual user ID)
GET http://localhost:4002/api/bookings/user/USER_ID_HERE
Content-Type: application/json

### ===== GUESTS =====

### Get all guests
GET http://localhost:4002/api/guests
Content-Type: application/json

### Get guests with filters
GET http://localhost:4002/api/guests?status=PENDING&visitDate=2024-01-15
Content-Type: application/json

### Create a new guest invitation
POST http://localhost:4002/api/guests
Content-Type: application/json
x-user-id: HOST_USER_ID_HERE

{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "company": "Example Corp",
  "visitDate": "2024-01-15",
  "visitTime": "14:30",
  "bookingId": "BOOKING_ID_HERE",
  "instructions": "Please bring ID"
}

### Get guest by ID (replace with actual ID)
GET http://localhost:4002/api/guests/GUEST_ID_HERE
Content-Type: application/json
x-user-id: HOST_USER_ID_HERE

### Update guest (replace with actual ID)
PUT http://localhost:4002/api/guests/GUEST_ID_HERE
Content-Type: application/json
x-user-id: HOST_USER_ID_HERE

{
  "name": "John Updated Doe",
  "company": "Updated Corp"
}

### Check in guest (replace with actual ID)
POST http://localhost:4002/api/guests/GUEST_ID_HERE/check-in
Content-Type: application/json

### Check out guest (replace with actual ID)
POST http://localhost:4002/api/guests/GUEST_ID_HERE/check-out
Content-Type: application/json

### Mark guest as no-show (replace with actual ID)
POST http://localhost:4002/api/guests/GUEST_ID_HERE/no-show
Content-Type: application/json

### Bulk operations on guests
POST http://localhost:4002/api/guests/bulk
Content-Type: application/json

{
  "guestIds": ["guest-id-1", "guest-id-2"],
  "operation": "check-in",
  "reason": "Bulk check-in for event"
}

### Get daily guests
GET http://localhost:4002/api/guests/daily?date=2024-01-15
Content-Type: application/json

### Delete guest (replace with actual ID)
DELETE http://localhost:4002/api/guests/GUEST_ID_HERE
Content-Type: application/json
x-user-id: HOST_USER_ID_HERE

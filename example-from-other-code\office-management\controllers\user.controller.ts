import { Request, Response } from 'express';
import { validationResult } from 'express-validator';
import { OfficeUserModel } from '../models/user.model';
import { BookingModel } from '../models/booking.model';
import { GuestModel } from '../models/guest.model';

export class UserController {
  // Get current user profile
  async getProfile(req: Request, res: Response) {
    try {
      const userId = req.user?.id;
      
      const user = await OfficeUserModel.findById(userId)
        .populate('manager', 'firstName lastName email position')
        .select('-__v');

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      res.json({
        success: true,
        data: user
      });
    } catch (error) {
      console.error('Error getting user profile:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Update current user profile
  async updateProfile(req: Request, res: Response) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation errors',
          errors: errors.array()
        });
      }

      const userId = req.user?.id;
      const allowedUpdates = [
        'phone', 'officeLocation', 'workSchedule', 'preferences', 
        'emergencyContact', 'profilePicture', 'slackUserId'
      ];

      const updateData: any = {};
      Object.keys(req.body).forEach(key => {
        if (allowedUpdates.includes(key)) {
          updateData[key] = req.body[key];
        }
      });

      const user = await OfficeUserModel.findByIdAndUpdate(
        userId,
        updateData,
        { new: true, runValidators: true }
      ).populate('manager', 'firstName lastName email position');

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      res.json({
        success: true,
        message: 'Profile updated successfully',
        data: user
      });
    } catch (error) {
      console.error('Error updating profile:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Get all users (admin only)
  async getUsers(req: Request, res: Response) {
    try {
      const {
        department,
        isActive,
        floor,
        page = 1,
        limit = 10,
        search
      } = req.query;

      const query: any = {};

      if (department) {
        query.department = department;
      }

      if (isActive !== undefined) {
        query.isActive = isActive === 'true';
      }

      if (floor) {
        query['officeLocation.floor'] = floor;
      }

      if (search) {
        query.$or = [
          { firstName: { $regex: search, $options: 'i' } },
          { lastName: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } },
          { employeeId: { $regex: search, $options: 'i' } }
        ];
      }

      const skip = (Number(page) - 1) * Number(limit);
      const users = await OfficeUserModel.find(query)
        .populate('manager', 'firstName lastName email position')
        .select('-__v')
        .sort({ firstName: 1, lastName: 1 })
        .skip(skip)
        .limit(Number(limit));

      const total = await OfficeUserModel.countDocuments(query);

      res.json({
        success: true,
        data: users,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      });
    } catch (error) {
      console.error('Error getting users:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Get specific user (admin only)
  async getUserById(req: Request, res: Response) {
    try {
      const { id } = req.params;

      const user = await OfficeUserModel.findById(id)
        .populate('manager', 'firstName lastName email position')
        .select('-__v');

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      res.json({
        success: true,
        data: user
      });
    } catch (error) {
      console.error('Error getting user:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Create new user (admin only)
  async createUser(req: Request, res: Response) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation errors',
          errors: errors.array()
        });
      }

      const user = new OfficeUserModel(req.body);
      await user.save();

      const populatedUser = await OfficeUserModel.findById(user._id)
        .populate('manager', 'firstName lastName email position');

      res.status(201).json({
        success: true,
        message: 'User created successfully',
        data: populatedUser
      });
    } catch (error) {
      console.error('Error creating user:', error);
      if (error instanceof Error && error.message.includes('duplicate key')) {
        return res.status(400).json({
          success: false,
          message: 'Employee ID or email already exists'
        });
      }
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Update user (admin only)
  async updateUser(req: Request, res: Response) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation errors',
          errors: errors.array()
        });
      }

      const { id } = req.params;

      const user = await OfficeUserModel.findByIdAndUpdate(
        id,
        req.body,
        { new: true, runValidators: true }
      ).populate('manager', 'firstName lastName email position');

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      res.json({
        success: true,
        message: 'User updated successfully',
        data: user
      });
    } catch (error) {
      console.error('Error updating user:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Deactivate user (admin only)
  async deactivateUser(req: Request, res: Response) {
    try {
      const { id } = req.params;

      const user = await OfficeUserModel.findByIdAndUpdate(
        id,
        { isActive: false },
        { new: true }
      );

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      res.json({
        success: true,
        message: 'User deactivated successfully',
        data: user
      });
    } catch (error) {
      console.error('Error deactivating user:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Activate user (admin only)
  async activateUser(req: Request, res: Response) {
    try {
      const { id } = req.params;

      const user = await OfficeUserModel.findByIdAndUpdate(
        id,
        { isActive: true },
        { new: true }
      );

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      res.json({
        success: true,
        message: 'User activated successfully',
        data: user
      });
    } catch (error) {
      console.error('Error activating user:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Get user's team members
  async getTeamMembers(req: Request, res: Response) {
    try {
      const { id } = req.params;

      const user = await OfficeUserModel.findById(id);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      const teamMembers = await OfficeUserModel.find({
        department: user.department,
        isActive: true,
        _id: { $ne: id }
      }).select('firstName lastName email position officeLocation');

      res.json({
        success: true,
        data: teamMembers
      });
    } catch (error) {
      console.error('Error getting team members:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Get user's direct reports (for managers)
  async getDirectReports(req: Request, res: Response) {
    try {
      const { id } = req.params;

      const directReports = await OfficeUserModel.find({
        manager: id,
        isActive: true
      }).select('firstName lastName email position department officeLocation');

      res.json({
        success: true,
        data: directReports
      });
    } catch (error) {
      console.error('Error getting direct reports:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Get user's booking history
  async getUserBookings(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { startDate, endDate, page = 1, limit = 10 } = req.query;

      const query: any = { userId: id };

      if (startDate || endDate) {
        query.startDate = {};
        if (startDate) query.startDate.$gte = new Date(startDate as string);
        if (endDate) query.startDate.$lte = new Date(endDate as string);
      }

      const skip = (Number(page) - 1) * Number(limit);
      const bookings = await BookingModel.find(query)
        .populate('resourceId')
        .sort({ startDate: -1 })
        .skip(skip)
        .limit(Number(limit));

      const total = await BookingModel.countDocuments(query);

      res.json({
        success: true,
        data: bookings,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      });
    } catch (error) {
      console.error('Error getting user bookings:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Get user's guest history
  async getUserGuests(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { startDate, endDate, page = 1, limit = 10 } = req.query;

      const query: any = { hostUserId: id };

      if (startDate || endDate) {
        query.visitDate = {};
        if (startDate) query.visitDate.$gte = new Date(startDate as string);
        if (endDate) query.visitDate.$lte = new Date(endDate as string);
      }

      const skip = (Number(page) - 1) * Number(limit);
      const guests = await GuestModel.find(query)
        .sort({ visitDate: -1 })
        .skip(skip)
        .limit(Number(limit));

      const total = await GuestModel.countDocuments(query);

      res.json({
        success: true,
        data: guests,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      });
    } catch (error) {
      console.error('Error getting user guests:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Search users
  async searchUsers(req: Request, res: Response) {
    try {
      const { q, department, limit = 10 } = req.query;

      if (!q) {
        return res.status(400).json({
          success: false,
          message: 'Search query is required'
        });
      }

      const query: any = {
        isActive: true,
        $or: [
          { firstName: { $regex: q, $options: 'i' } },
          { lastName: { $regex: q, $options: 'i' } },
          { email: { $regex: q, $options: 'i' } },
          { employeeId: { $regex: q, $options: 'i' } }
        ]
      };

      if (department) {
        query.department = department;
      }

      const users = await OfficeUserModel.find(query)
        .select('firstName lastName email position department officeLocation')
        .limit(Number(limit));

      res.json({
        success: true,
        data: users
      });
    } catch (error) {
      console.error('Error searching users:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Get departments list
  async getDepartments(req: Request, res: Response) {
    try {
      const departments = await OfficeUserModel.distinct('department', { isActive: true });
      
      res.json({
        success: true,
        data: departments.sort()
      });
    } catch (error) {
      console.error('Error getting departments:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Get positions list
  async getPositions(req: Request, res: Response) {
    try {
      const positions = await OfficeUserModel.distinct('position', { isActive: true });
      
      res.json({
        success: true,
        data: positions.sort()
      });
    } catch (error) {
      console.error('Error getting positions:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Delete user (admin only)
  async deleteUser(req: Request, res: Response) {
    try {
      const { id } = req.params;

      // Check if user has active bookings or guests
      const activeBookings = await BookingModel.countDocuments({
        userId: id,
        status: 'confirmed',
        startDate: { $gte: new Date() }
      });

      const activeGuests = await GuestModel.countDocuments({
        hostUserId: id,
        status: { $in: ['invited', 'confirmed'] },
        visitDate: { $gte: new Date() }
      });

      if (activeBookings > 0 || activeGuests > 0) {
        return res.status(400).json({
          success: false,
          message: 'Cannot delete user with active bookings or guests. Please deactivate instead.'
        });
      }

      const user = await OfficeUserModel.findByIdAndDelete(id);

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      res.json({
        success: true,
        message: 'User deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting user:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
}

### OCN House API Endpoints
### Base URL: http://localhost:3000/api

### ============================================
### MEETING ROOMS ENDPOINTS
### ============================================

### Get all meeting rooms
GET http://localhost:3000/api/meeting-rooms
Content-Type: application/json

### Get all meeting rooms with filters
GET http://localhost:3000/api/meeting-rooms?page=1&limit=10&search=sala&floor=3&capacity=8
Content-Type: application/json

### Get meeting room by ID
GET http://localhost:3000/api/meeting-rooms/507f1f77bcf86cd799439011
Content-Type: application/json

### Create new meeting room
POST http://localhost:3000/api/meeting-rooms
Content-Type: application/json

{
  "name": "Sala Ejecutiva A",
  "location": {
    "building": "Torre Principal",
    "floor": "3",
    "room": "301A",
    "address": "Av. Revolución 123, CDMX"
  },
  "capacity": 8,
  "amenities": ["WiFi", "Proyector", "TV", "Pizarra", "Café"],
  "equipment": ["Laptop", "Monitor", "Webcam", "Micrófono"],
  "description": "Sala ejecutiva con vista panorámica",
  "images": ["https://example.com/image1.jpg"],
  "bookingRules": {
    "minBookingDuration": 15,
    "maxBookingDuration": 480,
    "advanceBookingDays": 30,
    "minNoticeHours": 2,
    "requiresApproval": false
  },
  "availability": {
    "businessHours": {
      "start": "08:00",
      "end": "18:00"
    },
    "workingDays": [1, 2, 3, 4, 5],
    "timezone": "America/Mexico_City"
  }
}

### Update meeting room
PUT http://localhost:3000/api/meeting-rooms/507f1f77bcf86cd799439011
Content-Type: application/json

{
  "name": "Sala Ejecutiva A - Actualizada",
  "capacity": 10,
  "isActive": true,
  "amenities": ["WiFi", "Proyector", "TV", "Pizarra", "Café", "Aire Acondicionado"]
}

### Delete meeting room
DELETE http://localhost:3000/api/meeting-rooms/507f1f77bcf86cd799439011
Content-Type: application/json

### Check meeting room availability
GET http://localhost:3000/api/meeting-rooms/507f1f77bcf86cd799439011/availability?startDate=2024-01-15T09:00:00.000Z&endDate=2024-01-15T17:00:00.000Z
Content-Type: application/json

### Get meeting room bookings
GET http://localhost:3000/api/meeting-rooms/507f1f77bcf86cd799439011/bookings?startDate=2024-01-01&endDate=2024-01-31
Content-Type: application/json

### ============================================
### DESKS ENDPOINTS
### ============================================

### Get all desks
GET http://localhost:3000/api/desks
Content-Type: application/json

### Get desks with filters
GET http://localhost:3000/api/desks?page=1&limit=10&search=escritorio&floor=2&type=STANDING&zone=Norte
Content-Type: application/json

### Get desk by ID
GET http://localhost:3000/api/desks/507f1f77bcf86cd799439012
Content-Type: application/json

### Create new desk
POST http://localhost:3000/api/desks
Content-Type: application/json

{
  "name": "Escritorio A-01",
  "type": "STANDING",
  "location": {
    "building": "Torre Principal",
    "floor": "2",
    "zone": "Norte",
    "position": "Ventana",
    "address": "Av. Revolución 123, CDMX"
  },
  "description": "Escritorio standing con vista al jardín",
  "amenities": ["WiFi", "Monitor", "Lámpara", "Cajones"],
  "equipment": ["Monitor Secundario", "Teclado", "Mouse", "Hub USB"],
  "images": ["https://example.com/desk1.jpg"],
  "bookingRules": {
    "advanceBookingDays": 7,
    "requiresApproval": false
  }
}

### Update desk
PUT http://localhost:3000/api/desks/507f1f77bcf86cd799439012
Content-Type: application/json

{
  "name": "Escritorio A-01 - Premium",
  "type": "EXECUTIVE",
  "amenities": ["WiFi", "Monitor", "Lámpara", "Cajones", "Cargador USB"]
}

### Delete desk
DELETE http://localhost:3000/api/desks/507f1f77bcf86cd799439012
Content-Type: application/json

### Check desk availability
GET http://localhost:3000/api/desks/507f1f77bcf86cd799439012/availability?date=2024-01-15
Content-Type: application/json

### Get desk statistics
GET http://localhost:3000/api/desks/statistics
Content-Type: application/json

### ============================================
### BOOKINGS ENDPOINTS
### ============================================

### Get all bookings (admin)
GET http://localhost:3000/api/bookings
Content-Type: application/json

### Get user bookings
GET http://localhost:3000/api/bookings/user?page=1&limit=10&resourceType=MEETING_ROOM&status=CONFIRMED&upcoming=true
Content-Type: application/json

### Get booking by ID
GET http://localhost:3000/api/bookings/507f1f77bcf86cd799439013
Content-Type: application/json

### Create new booking
POST http://localhost:3000/api/bookings
Content-Type: application/json

{
  "title": "Reunión de Equipo",
  "description": "Reunión semanal del equipo de desarrollo",
  "resourceType": "MEETING_ROOM",
  "resourceId": "507f1f77bcf86cd799439011",
  "startDate": "2024-01-15T09:00:00.000Z",
  "endDate": "2024-01-15T10:00:00.000Z",
  "guests": [
    {
      "name": "Juan Pérez",
      "email": "<EMAIL>",
      "phone": "+52 55 1234 5678",
      "company": "Cliente ABC"
    }
  ]
}

### Update booking
PUT http://localhost:3000/api/bookings/507f1f77bcf86cd799439013
Content-Type: application/json

{
  "title": "Reunión de Equipo - Actualizada",
  "description": "Reunión semanal con agenda extendida",
  "endDate": "2024-01-15T11:00:00.000Z"
}

### Cancel booking
POST http://localhost:3000/api/bookings/507f1f77bcf86cd799439013/cancel
Content-Type: application/json

{
  "reason": "Cambio de horario por emergencia"
}

### Confirm booking
POST http://localhost:3000/api/bookings/507f1f77bcf86cd799439013/confirm
Content-Type: application/json

### Get upcoming bookings
GET http://localhost:3000/api/bookings/upcoming?limit=5
Content-Type: application/json

### ============================================
### GUESTS ENDPOINTS
### ============================================

### Get all guests (admin)
GET http://localhost:3000/api/guests
Content-Type: application/json

### Get user guests
GET http://localhost:3000/api/guests/user?page=1&limit=10&status=CONFIRMED&upcoming=true
Content-Type: application/json

### Get guest by ID
GET http://localhost:3000/api/guests/507f1f77bcf86cd799439014
Content-Type: application/json

### Create/invite new guest
POST http://localhost:3000/api/guests
Content-Type: application/json

{
  "name": "María González",
  "email": "<EMAIL>",
  "phone": "+52 55 9876 5432",
  "company": "Cliente XYZ",
  "visitDate": "2024-01-15T14:00:00.000Z",
  "purpose": "Reunión comercial",
  "hostNotes": "Cliente potencial importante",
  "bookingId": "507f1f77bcf86cd799439013"
}

### Update guest
PUT http://localhost:3000/api/guests/507f1f77bcf86cd799439014
Content-Type: application/json

{
  "name": "María González Rodríguez",
  "phone": "+52 55 9876 5432",
  "company": "Cliente XYZ S.A. de C.V.",
  "hostNotes": "Cliente VIP - trato preferencial"
}

### Check-in guest
POST http://localhost:3000/api/guests/507f1f77bcf86cd799439014/checkin
Content-Type: application/json

{
  "notes": "Llegó puntual, dirigido a sala de reuniones"
}

### Check-out guest
POST http://localhost:3000/api/guests/507f1f77bcf86cd799439014/checkout
Content-Type: application/json

{
  "notes": "Reunión exitosa, interesado en propuesta"
}

### Send guest invitation
POST http://localhost:3000/api/guests/507f1f77bcf86cd799439014/send-invitation
Content-Type: application/json

### Get today's guests
GET http://localhost:3000/api/guests/today
Content-Type: application/json

### Get upcoming guests
GET http://localhost:3000/api/guests/upcoming?limit=5
Content-Type: application/json

### Bulk guest operations
POST http://localhost:3000/api/guests/bulk
Content-Type: application/json

{
  "operation": "CANCEL",
  "guestIds": ["507f1f77bcf86cd799439014", "507f1f77bcf86cd799439015"],
  "reason": "Evento cancelado por emergencia"
}

### ============================================
### QUERY PARAMETERS REFERENCE
### ============================================

### Common Query Parameters:
# page: number (default: 1)
# limit: number (default: 10, max: 100)
# search: string (searches in name, description)
# sortBy: string (field to sort by)
# sortOrder: "asc" | "desc" (default: "desc")

### Meeting Rooms Specific:
# floor: string
# capacity: number (minimum capacity)
# amenities: string[] (comma-separated)
# isActive: boolean

### Desks Specific:
# floor: string
# zone: string
# type: "REGULAR" | "STANDING" | "EXECUTIVE"

### Bookings Specific:
# resourceType: "MEETING_ROOM" | "DESK"
# status: "PENDING" | "CONFIRMED" | "CANCELLED" | "COMPLETED"
# startDate: ISO string
# endDate: ISO string
# upcoming: boolean
# past: boolean

### Guests Specific:
# status: "PENDING" | "CONFIRMED" | "CHECKED_IN" | "CHECKED_OUT" | "CANCELLED" | "NO_SHOW"
# visitDate: ISO string
# upcoming: boolean
# past: boolean

### ============================================
### RESPONSE FORMATS
### ============================================

### Success Response:
# {
#   "success": true,
#   "data": T,
#   "pagination": {
#     "page": number,
#     "limit": number,
#     "total": number,
#     "totalPages": number,
#     "hasNext": boolean,
#     "hasPrev": boolean
#   }
# }

### Error Response:
# {
#   "success": false,
#   "error": "Error message description"
# }

### ============================================
### NOTIFICATIONS ENDPOINTS
### ============================================

### Send email notification
POST http://localhost:3000/api/notifications/email
Content-Type: application/json

{
  "to": "<EMAIL>",
  "subject": "Confirmación de Reserva",
  "template": "booking-confirmation",
  "data": {
    "userName": "Juan Pérez",
    "bookingTitle": "Reunión de Equipo",
    "startDate": "2024-01-15T09:00:00.000Z",
    "endDate": "2024-01-15T10:00:00.000Z",
    "resourceName": "Sala Ejecutiva A"
  }
}

### Send Slack notification
POST http://localhost:3000/api/notifications/slack
Content-Type: application/json

{
  "channel": "#general",
  "message": "Nueva reserva creada",
  "blocks": [
    {
      "type": "section",
      "text": {
        "type": "mrkdwn",
        "text": "*Nueva Reserva Creada*\n*Sala:* Sala Ejecutiva A\n*Usuario:* Juan Pérez\n*Fecha:* 15 Enero 2024, 09:00-10:00"
      }
    }
  ],
  "actions": [
    {
      "type": "button",
      "text": "Aprobar",
      "value": "approve_507f1f77bcf86cd799439013",
      "style": "primary"
    },
    {
      "type": "button",
      "text": "Rechazar",
      "value": "reject_507f1f77bcf86cd799439013",
      "style": "danger"
    }
  ]
}

### ============================================
### STATISTICS ENDPOINTS
### ============================================

### Get dashboard statistics
GET http://localhost:3000/api/statistics/dashboard
Content-Type: application/json

### Get booking statistics
GET http://localhost:3000/api/statistics/bookings?startDate=2024-01-01&endDate=2024-01-31
Content-Type: application/json

### Get resource utilization
GET http://localhost:3000/api/statistics/utilization?resourceType=MEETING_ROOM&period=month
Content-Type: application/json

### Get guest statistics
GET http://localhost:3000/api/statistics/guests?period=week
Content-Type: application/json

### ============================================
### ADMIN ENDPOINTS
### ============================================

### Get system health
GET http://localhost:3000/api/admin/health
Content-Type: application/json

### Get audit logs
GET http://localhost:3000/api/admin/audit-logs?page=1&limit=50&action=CREATE&resource=BOOKING
Content-Type: application/json

### Update system settings
PUT http://localhost:3000/api/admin/settings
Content-Type: application/json

{
  "businessHours": {
    "start": "08:00",
    "end": "18:00"
  },
  "workingDays": [1, 2, 3, 4, 5],
  "timezone": "America/Mexico_City",
  "defaultBookingDuration": 60,
  "maxAdvanceBookingDays": 30,
  "emailNotifications": true,
  "slackNotifications": true,
  "requireApprovalForLongBookings": true,
  "longBookingThresholdHours": 4
}

### ============================================
### WEBHOOK ENDPOINTS (For Slack Integration)
### ============================================

### Slack interactive components webhook
POST http://localhost:3000/api/webhooks/slack/interactive
Content-Type: application/x-www-form-urlencoded

payload={"type":"block_actions","user":{"id":"U123456","name":"john.doe"},"actions":[{"action_id":"approve_booking","value":"507f1f77bcf86cd799439013"}],"response_url":"https://hooks.slack.com/actions/..."}

### Slack slash commands webhook
POST http://localhost:3000/api/webhooks/slack/commands
Content-Type: application/x-www-form-urlencoded

token=verification_token&team_id=T123456&team_domain=onecarnow&channel_id=C123456&channel_name=general&user_id=U123456&user_name=john.doe&command=/book-room&text=sala ejecutiva mañana 9am&response_url=https://hooks.slack.com/commands/...

### ============================================
### ERROR EXAMPLES
### ============================================

### Validation Error Example
POST http://localhost:3000/api/bookings
Content-Type: application/json

{
  "title": "",
  "resourceType": "INVALID_TYPE",
  "startDate": "invalid-date"
}

# Expected Response:
# {
#   "success": false,
#   "error": "Validation failed: title is required, resourceType must be MEETING_ROOM or DESK, startDate must be a valid ISO date"
# }

### Resource Not Found Example
GET http://localhost:3000/api/meeting-rooms/invalid-id
Content-Type: application/json

# Expected Response:
# {
#   "success": false,
#   "error": "Meeting room not found"
# }

### Booking Conflict Example
POST http://localhost:3000/api/bookings
Content-Type: application/json

{
  "title": "Conflicting Meeting",
  "resourceType": "MEETING_ROOM",
  "resourceId": "507f1f77bcf86cd799439011",
  "startDate": "2024-01-15T09:30:00.000Z",
  "endDate": "2024-01-15T10:30:00.000Z"
}

# Expected Response (if room is already booked):
# {
#   "success": false,
#   "error": "Resource is not available during the requested time slot"
# }

### ============================================
### AUTHENTICATION HEADERS (When implemented)
### ============================================

### Authorization: Bearer <jwt_token>
### X-User-Email: <EMAIL>
### X-User-Role: admin | user | reception

### ============================================
### RATE LIMITING
### ============================================

### Rate limits (when implemented):
# - General API: 100 requests per minute per IP
# - Authentication: 10 requests per minute per IP
# - File uploads: 5 requests per minute per user
# - Webhooks: 1000 requests per minute per source

### Headers returned with rate limiting:
# X-RateLimit-Limit: 100
# X-RateLimit-Remaining: 95
# X-RateLimit-Reset: 1640995200

### ============================================
### WEBSOCKET ENDPOINTS (Future implementation)
### ============================================

### Real-time booking updates
# ws://localhost:3000/api/ws/bookings

### Real-time guest check-ins
# ws://localhost:3000/api/ws/guests

### Real-time notifications
# ws://localhost:3000/api/ws/notifications

import { t } from 'elysia';

export const createGuest = t.Object({
  name: t.String(),
  email: t.Optional(t.String()),
  phone: t.Optional(t.String()),
  company: t.Optional(t.String()),
  visitDate: t.String(),
  visitTime: t.String(),
  bookingId: t.Optional(t.String()),
  instructions: t.Optional(t.String())
});

export const updateGuest = t.Object({
  name: t.Optional(t.String()),
  email: t.Optional(t.String()),
  phone: t.Optional(t.String()),
  company: t.Optional(t.String()),
  visitDate: t.Optional(t.String()),
  visitTime: t.Optional(t.String()),
  bookingId: t.Optional(t.String()),
  instructions: t.Optional(t.String())
});

export const getGuestsQuery = t.Object({
  page: t.Optional(t.Numeric()),
  limit: t.Optional(t.Numeric()),
  startDate: t.Optional(t.String()),
  endDate: t.Optional(t.String()),
  status: t.Optional(t.Union([
    t.Literal('PENDING'),
    t.Literal('CHECKED_IN'),
    t.Literal('CHECKED_OUT'),
    t.Literal('NO_SHOW')
  ])),
  hostId: t.Optional(t.String()),
  visitDate: t.Optional(t.String()),
  search: t.Optional(t.String())
});

export const guestParams = t.Object({
  id: t.String()
});

export const checkInOutGuest = t.Object({
  action: t.Union([t.Literal('check-in'), t.Literal('check-out')]),
  time: t.Optional(t.String())
});

export const bulkGuestOperation = t.Object({
  guestIds: t.Array(t.String()),
  operation: t.Union([
    t.Literal('check-in'),
    t.Literal('check-out'),
    t.Literal('mark-no-show'),
    t.Literal('delete')
  ]),
  reason: t.Optional(t.String())
});

export const sendInvitation = t.Object({
  guestId: t.String(),
  method: t.Union([t.Literal('email'), t.Literal('whatsapp')]),
  message: t.Optional(t.String())
});

export const getDailyGuestsQuery = t.Object({
  date: t.Optional(t.String()),
  status: t.Optional(t.Union([
    t.Literal('PENDING'),
    t.Literal('CHECKED_IN'),
    t.Literal('CHECKED_OUT'),
    t.Literal('NO_SHOW')
  ])),
  hostId: t.Optional(t.String())
});

export type CreateGuest = typeof createGuest.static;
export type UpdateGuest = typeof updateGuest.static;
export type GetGuestsQuery = typeof getGuestsQuery.static;
export type GuestParams = typeof guestParams.static;
export type CheckInOutGuest = typeof checkInOutGuest.static;
export type BulkGuestOperation = typeof bulkGuestOperation.static;
export type SendInvitation = typeof sendInvitation.static;
export type GetDailyGuestsQuery = typeof getDailyGuestsQuery.static;

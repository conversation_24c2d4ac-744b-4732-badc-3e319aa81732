import { Router } from 'express';
import { NotificationController } from '../controllers/notification.controller';
import { NotificationValidator } from '../validators/notification.validator';
import { verifyToken } from '../../middlewares/verifyToken';

const router = Router();
const notificationController = new NotificationController();

// Apply authentication middleware to all routes
router.use(verifyToken);

// Custom notification routes
router.post('/custom', 
  NotificationValidator.sendCustomNotification,
  notificationController.sendCustomNotification.bind(notificationController)
);

router.post('/bulk', 
  NotificationValidator.sendBulkNotifications,
  notificationController.sendBulkNotifications.bind(notificationController)
);

router.post('/department', 
  NotificationValidator.sendDepartmentNotification,
  notificationController.sendDepartmentNotification.bind(notificationController)
);

// Specific notification routes
router.post('/booking/:bookingId/reminder', 
  NotificationValidator.sendBookingReminder,
  notificationController.sendBookingReminder.bind(notificationController)
);

router.post('/guest/:guestId/invitation', 
  NotificationValidator.sendGuestInvitation,
  notificationController.sendGuestInvitation.bind(notificationController)
);

// Status and testing routes
router.get('/status/:type/:id', 
  NotificationValidator.getNotificationStatus,
  notificationController.getNotificationStatus.bind(notificationController)
);

router.post('/test', 
  NotificationValidator.testNotificationServices,
  notificationController.testNotificationServices.bind(notificationController)
);

export default router;

import { t } from 'elysia';

export const createMeetingRoom = t.Object({
  name: t.String(),
  location: t.Object({
    building: t.Optional(t.String()),
    floor: t.String(),
    room: t.String(),
    address: t.Optional(t.String())
  }),
  capacity: t.Number(),
  amenities: t.Optional(t.Array(t.String())),
  equipment: t.Optional(t.Array(t.String())),
  description: t.Optional(t.String()),
  isActive: t.Optional(t.<PERSON>()),
  images: t.Optional(t.Array(t.String())),
  bookingRules: t.Optional(
    t.Object({
      minBookingDuration: t.Optional(t.Number()),
      maxBookingDuration: t.Optional(t.Number()),
      advanceBookingDays: t.Optional(t.Number()),
      minNoticeHours: t.Optional(t.Number())
    })
  ),
  availability: t.Optional(
    t.Object({
      businessHours: t.Optional(t.Object({
        start: t.String(),
        end: t.String()
      })),
      workingDays: t.Optional(t.Array(t.Number())),
      timezone: t.Optional(t.String())
    })
  )
});

export const updateMeetingRoom = t.Object({
  name: t.Optional(t.String()),
  location: t.Optional(t.Object({
    building: t.Optional(t.String()),
    floor: t.String(),
    room: t.String(),
    address: t.Optional(t.String())
  })),
  capacity: t.Optional(t.Number()),
  amenities: t.Optional(t.Array(t.String())),
  equipment: t.Optional(t.Array(t.String())),
  description: t.Optional(t.String()),
  isActive: t.Optional(t.Boolean()),
  images: t.Optional(t.Array(t.String())),
  bookingRules: t.Optional(
    t.Object({
      minBookingDuration: t.Optional(t.Number()),
      maxBookingDuration: t.Optional(t.Number()),
      advanceBookingDays: t.Optional(t.Number()),
      minNoticeHours: t.Optional(t.Number())
    })
  ),
  availability: t.Optional(
    t.Object({
      businessHours: t.Optional(t.Object({
        start: t.String(),
        end: t.String()
      })),
      workingDays: t.Optional(t.Array(t.Number())),
      timezone: t.Optional(t.String())
    })
  )
});

export const getMeetingRoomQuery = t.Object({
  page: t.Optional(t.Numeric()),
  limit: t.Optional(t.Numeric()),
  search: t.Optional(t.String()),
  isActive: t.Optional(t.Boolean()),
  capacity: t.Optional(t.Numeric()),
  floor: t.Optional(t.String()),
  amenities: t.Optional(t.Array(t.String())),
  equipment: t.Optional(t.Array(t.String()))
});

export const meetingRoomParams = t.Object({
  id: t.String()
});

export const checkAvailabilityQuery = t.Object({
  startDate: t.String(),
  endDate: t.String(),
  excludeBookingId: t.Optional(t.String())
});

export type CreateMeetingRoom = typeof createMeetingRoom.static;
export type UpdateMeetingRoom = typeof updateMeetingRoom.static;
export type GetMeetingRoomQuery = typeof getMeetingRoomQuery.static;
export type MeetingRoomParams = typeof meetingRoomParams.static;
export type CheckAvailabilityQuery = typeof checkAvailabilityQuery.static;

### Office Management - Users API Tests

@baseUrl = http://localhost:3000
@authToken = your-jwt-token-here

### Get current user profile
GET {{baseUrl}}/api/office-management/users/profile
Authorization: Bearer {{authToken}}

### Update current user profile
PUT {{baseUrl}}/api/office-management/users/profile
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "phone": "+1234567890",
  "officeLocation": {
    "floor": "3rd Floor",
    "desk": "Desk 301"
  },
  "workSchedule": {
    "workingDays": [1, 2, 3, 4, 5],
    "startTime": "09:00",
    "endTime": "18:00"
  },
  "preferences": {
    "defaultMeetingDuration": 60,
    "notificationSettings": {
      "email": true,
      "slack": true,
      "whatsapp": false
    }
  },
  "emergencyContact": {
    "name": "Jane Doe",
    "phone": "+0987654321",
    "relationship": "Spouse"
  }
}

### Get all users (admin only)
GET {{baseUrl}}/api/office-management/users
Authorization: Bearer {{authToken}}

### Get users with filters (admin only)
GET {{baseUrl}}/api/office-management/users?department=Engineering&isActive=true&floor=2
Authorization: Bearer {{authToken}}

### Get specific user (admin only)
GET {{baseUrl}}/api/office-management/users/60f7b3b3b3b3b3b3b3b3b3b7
Authorization: Bearer {{authToken}}

### Create new user (admin only)
POST {{baseUrl}}/api/office-management/users
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "employeeId": "EMP001",
  "firstName": "John",
  "lastName": "Smith",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "department": "Engineering",
  "position": "Software Developer",
  "manager": "60f7b3b3b3b3b3b3b3b3b3b8",
  "officeLocation": {
    "building": "Main Building",
    "floor": "2nd Floor",
    "desk": "Desk 201"
  },
  "workSchedule": {
    "workingDays": [1, 2, 3, 4, 5],
    "startTime": "09:00",
    "endTime": "18:00",
    "timezone": "America/Mexico_City"
  },
  "permissions": {
    "canBookMeetingRooms": true,
    "canBookDesks": true,
    "canManageGuests": false,
    "canViewReports": false,
    "isAdmin": false,
    "maxBookingDuration": 8,
    "maxAdvanceBookingDays": 30
  },
  "slackUserId": "U1234567890"
}

### Update user (admin only)
PUT {{baseUrl}}/api/office-management/users/60f7b3b3b3b3b3b3b3b3b3b7
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "position": "Senior Software Developer",
  "permissions": {
    "canBookMeetingRooms": true,
    "canBookDesks": true,
    "canManageGuests": true,
    "canViewReports": true,
    "isAdmin": false,
    "maxBookingDuration": 12,
    "maxAdvanceBookingDays": 60
  }
}

### Deactivate user (admin only)
PATCH {{baseUrl}}/api/office-management/users/60f7b3b3b3b3b3b3b3b3b3b7/deactivate
Authorization: Bearer {{authToken}}

### Activate user (admin only)
PATCH {{baseUrl}}/api/office-management/users/60f7b3b3b3b3b3b3b3b3b3b7/activate
Authorization: Bearer {{authToken}}

### Update user permissions (admin only)
PATCH {{baseUrl}}/api/office-management/users/60f7b3b3b3b3b3b3b3b3b3b7/permissions
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "canBookMeetingRooms": true,
  "canBookDesks": true,
  "canManageGuests": true,
  "canViewReports": false,
  "isAdmin": false,
  "maxBookingDuration": 8,
  "maxAdvanceBookingDays": 45
}

### Search users
GET {{baseUrl}}/api/office-management/users/search?q=john&department=Engineering
Authorization: Bearer {{authToken}}

### Get user's team members
GET {{baseUrl}}/api/office-management/users/60f7b3b3b3b3b3b3b3b3b3b7/team
Authorization: Bearer {{authToken}}

### Get user's direct reports (for managers)
GET {{baseUrl}}/api/office-management/users/60f7b3b3b3b3b3b3b3b3b3b7/reports
Authorization: Bearer {{authToken}}

### Get user's booking history
GET {{baseUrl}}/api/office-management/users/60f7b3b3b3b3b3b3b3b3b3b7/bookings?startDate=2024-01-01&endDate=2024-01-31
Authorization: Bearer {{authToken}}

### Get user's guest history
GET {{baseUrl}}/api/office-management/users/60f7b3b3b3b3b3b3b3b3b3b7/guests?startDate=2024-01-01&endDate=2024-01-31
Authorization: Bearer {{authToken}}

### Get user statistics
GET {{baseUrl}}/api/office-management/users/60f7b3b3b3b3b3b3b3b3b3b7/stats?period=month
Authorization: Bearer {{authToken}}

### Update user's Slack integration
PATCH {{baseUrl}}/api/office-management/users/60f7b3b3b3b3b3b3b3b3b3b7/slack
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "slackUserId": "U0987654321"
}

### Upload user profile picture
POST {{baseUrl}}/api/office-management/users/profile/picture
Authorization: Bearer {{authToken}}
Content-Type: multipart/form-data

### Delete user profile picture
DELETE {{baseUrl}}/api/office-management/users/profile/picture
Authorization: Bearer {{authToken}}

### Get departments list
GET {{baseUrl}}/api/office-management/users/departments
Authorization: Bearer {{authToken}}

### Get positions list
GET {{baseUrl}}/api/office-management/users/positions
Authorization: Bearer {{authToken}}

### Get organization chart
GET {{baseUrl}}/api/office-management/users/org-chart
Authorization: Bearer {{authToken}}

### Bulk import users (admin only)
POST {{baseUrl}}/api/office-management/users/bulk-import
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "users": [
    {
      "employeeId": "EMP002",
      "firstName": "Jane",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "department": "Marketing",
      "position": "Marketing Manager"
    },
    {
      "employeeId": "EMP003",
      "firstName": "Bob",
      "lastName": "Johnson",
      "email": "<EMAIL>",
      "department": "Sales",
      "position": "Sales Representative"
    }
  ]
}

### Export users data (admin only)
GET {{baseUrl}}/api/office-management/users/export?format=csv&department=Engineering
Authorization: Bearer {{authToken}}

### Get user's notification preferences
GET {{baseUrl}}/api/office-management/users/profile/notifications
Authorization: Bearer {{authToken}}

### Update user's notification preferences
PUT {{baseUrl}}/api/office-management/users/profile/notifications
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "email": true,
  "slack": true,
  "whatsapp": false,
  "bookingReminders": true,
  "guestNotifications": true,
  "systemUpdates": false
}

### Delete user (admin only)
DELETE {{baseUrl}}/api/office-management/users/60f7b3b3b3b3b3b3b3b3b3b7
Authorization: Bearer {{authToken}}

### Get user's calendar integration
GET {{baseUrl}}/api/office-management/users/profile/calendar
Authorization: Bearer {{authToken}}

### Update user's calendar integration
PUT {{baseUrl}}/api/office-management/users/profile/calendar
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "provider": "google",
  "calendarId": "primary",
  "syncEnabled": true,
  "syncBookings": true,
  "syncGuests": false
}

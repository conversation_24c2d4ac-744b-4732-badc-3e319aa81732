import { BUSINESS_HOURS } from '../constants';

export class DateHelpers {
  /**
   * Check if a date is a weekend
   */
  static isWeekend(date: Date): boolean {
    const day = date.getDay();
    return day === 0 || day === 6; // Sunday = 0, Saturday = 6
  }

  /**
   * Check if a time is within business hours
   */
  static isWithinBusinessHours(time: string): boolean;
  static isWithinBusinessHours(
    startDate: Date,
    endDate: Date,
    businessHours: { start: string; end: string },
    workingDays: number[],
    timezone?: string
  ): boolean;
  static isWithinBusinessHours(
    timeOrStartDate: string | Date,
    endDate?: Date,
    businessHours?: { start: string; end: string },
    workingDays?: number[],
    timezone?: string
  ): boolean {
    // Original single time check
    if (typeof timeOrStartDate === 'string') {
      const [hours, minutes] = timeOrStartDate.split(':').map(Number);
      const timeInMinutes = hours * 60 + minutes;

      const [startHours, startMinutes] = BUSINESS_HOURS.START.split(':').map(Number);
      const startTimeInMinutes = startHours * 60 + startMinutes;

      const [endHours, endMinutes] = BUSINESS_HOURS.END.split(':').map(Number);
      const endTimeInMinutes = endHours * 60 + endMinutes;

      return timeInMinutes >= startTimeInMinutes && timeInMinutes <= endTimeInMinutes;
    }

    // New date range check
    if (endDate && businessHours && workingDays) {
      const startDate = timeOrStartDate as Date;

      // Check if dates are on working days
      const startDay = startDate.getDay();
      const endDay = endDate.getDay();

      if (!workingDays.includes(startDay) || !workingDays.includes(endDay)) {
        return false;
      }

      // If the booking spans multiple days, it's not allowed
      if (startDate.toDateString() !== endDate.toDateString()) {
        return false;
      }

      // Check if times are within business hours
      const startTime = this.formatTime(startDate);
      const endTime = this.formatTime(endDate);

      return startTime >= businessHours.start && endTime <= businessHours.end;
    }

    return false;
  }

  /**
   * Check if a date is in the past
   */
  static isPastDate(date: Date): boolean {
    const now = new Date();
    now.setHours(0, 0, 0, 0);
    date.setHours(0, 0, 0, 0);
    return date < now;
  }

  /**
   * Check if a date is too far in the future
   */
  static isTooFarInFuture(date: Date, maxDays: number): boolean {
    const now = new Date();
    const maxDate = new Date(now);
    maxDate.setDate(now.getDate() + maxDays);
    return date > maxDate;
  }

  /**
   * Format date to YYYY-MM-DD
   */
  static formatDate(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  /**
   * Format time to HH:MM
   */
  static formatTime(date: Date): string {
    return date.toTimeString().slice(0, 5);
  }

  /**
   * Parse date string to Date object
   */
  static parseDate(dateString: string): Date {
    return new Date(dateString);
  }

  /**
   * Get start and end of day
   */
  static getStartOfDay(date: Date): Date {
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    return startOfDay;
  }

  static getEndOfDay(date: Date): Date {
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);
    return endOfDay;
  }

  /**
   * Check if two time slots overlap
   */
  static doTimeSlotsOverlap(
    start1: Date,
    end1: Date,
    start2: Date,
    end2: Date
  ): boolean {
    return start1 < end2 && start2 < end1;
  }

  /**
   * Generate time slots for a given date
   */
  static generateTimeSlots(date: Date, durationHours: number = 1): string[] {
    const slots: string[] = [];
    const [startHours] = BUSINESS_HOURS.START.split(':').map(Number);
    const [endHours] = BUSINESS_HOURS.END.split(':').map(Number);

    for (let hour = startHours; hour < endHours; hour += durationHours) {
      const timeSlot = `${hour.toString().padStart(2, '0')}:00`;
      slots.push(timeSlot);
    }

    return slots;
  }
}

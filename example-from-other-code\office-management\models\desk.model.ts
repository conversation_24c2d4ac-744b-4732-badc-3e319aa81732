import mongoose, { Schema, Document } from 'mongoose';

export interface IDesk extends Document {
  name: string;
  number?: string;
  location: {
    building?: string;
    floor: string;
    zone: string;
    address?: string;
  };
  type: 'individual' | 'shared' | 'standing' | 'executive';
  amenities: string[];
  equipment: string[];
  description?: string;
  isActive: boolean;
  images?: string[];
  bookingRules: {
    advanceBookingDays: number; // how many days in advance can be booked
    minNoticeHours: number; // minimum hours notice required
    maxConsecutiveDays: number; // maximum consecutive days that can be booked
  };
  availability: {
    workingDays: number[]; // [1,2,3,4,5] for Monday-Friday
    timezone: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

const DeskSchema = new Schema<IDesk>({
  name: {
    type: String,
    required: true,
    trim: true,
    unique: true
  },
  number: {
    type: String,
    trim: true,
    sparse: true // Allows multiple null values but unique non-null values
  },
  location: {
    building: {
      type: String,
      trim: true
    },
    floor: {
      type: String,
      required: true,
      trim: true
    },
    zone: {
      type: String,
      required: true,
      trim: true
    },
    address: {
      type: String,
      trim: true
    }
  },
  type: {
    type: String,
    enum: ['individual', 'shared', 'standing', 'executive'],
    default: 'individual',
    required: true
  },
  amenities: [{
    type: String,
    trim: true
  }],
  equipment: [{
    type: String,
    trim: true
  }],
  description: {
    type: String,
    trim: true,
    maxlength: 500
  },
  isActive: {
    type: Boolean,
    default: true
  },
  images: [{
    type: String
  }],
  bookingRules: {
    advanceBookingDays: {
      type: Number,
      default: 30,
      min: 1,
      max: 90
    },
    minNoticeHours: {
      type: Number,
      default: 2,
      min: 0,
      max: 24
    },
    maxConsecutiveDays: {
      type: Number,
      default: 5,
      min: 1,
      max: 30
    }
  },
  availability: {
    workingDays: {
      type: [Number],
      default: [1, 2, 3, 4, 5], // Monday to Friday
      validate: {
        validator: function(days: number[]) {
          return days.every(day => day >= 0 && day <= 6);
        },
        message: 'Working days must be between 0 (Sunday) and 6 (Saturday)'
      }
    },
    timezone: {
      type: String,
      default: "America/Mexico_City"
    }
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      delete ret.__v;
      return ret;
    }
  }
});

// Indexes
DeskSchema.index({ name: 1 });
DeskSchema.index({ number: 1 });
DeskSchema.index({ isActive: 1 });
DeskSchema.index({ type: 1 });
DeskSchema.index({ 'location.floor': 1 });
DeskSchema.index({ 'location.zone': 1 });

export const DeskModel = mongoose.model<IDesk>('Desk', DeskSchema);

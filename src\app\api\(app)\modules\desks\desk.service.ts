import { prisma } from '@/db';
import { CreateDesk, UpdateDesk, GetDeskQuery, CheckAvailabilityQuery } from './desks.validators';

export class DeskService {
  static async createDesk(data: CreateDesk) {
    try {
      const desk = await prisma.desk.create({
        data: {
          ...data,
          location: data.location as any,
          equipment: data.equipment || [],
          images: data.images || [],
          isActive: data.isActive ?? true
        }
      });
      return desk;
    } catch (error: any) {
      if (error.code === 'P2002') {
        throw new Error('A desk with this name already exists');
      }
      throw new Error(`Failed to create desk: ${error.message}`);
    }
  }

  static async getAllDesks(query: GetDeskQuery = {}) {
    const {
      page = 1,
      limit = 10,
      search,
      isActive,
      floor,
      zone
    } = query;

    const skip = (page - 1) * limit;
    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (isActive !== undefined) {
      where.isActive = isActive;
    }

    if (floor) {
      where.location = {
        path: ['floor'],
        equals: floor
      };
    }

    if (zone) {
      where.location = {
        path: ['zone'],
        equals: zone
      };
    }

    const [desks, total] = await Promise.all([
      prisma.desk.findMany({
        where,
        skip,
        take: limit,
        orderBy: { name: 'asc' },
        include: {
          bookings: {
            where: {
              status: 'CONFIRMED',
              startDate: { gte: new Date() }
            },
            take: 5,
            orderBy: { startDate: 'asc' }
          }
        }
      }),
      prisma.desk.count({ where })
    ]);

    return {
      data: desks,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  static async getDeskById(id: string) {
    const desk = await prisma.desk.findUnique({
      where: { id },
      include: {
        bookings: {
          where: {
            status: 'CONFIRMED'
          },
          include: {
            user: {
              select: { id: true, name: true, email: true }
            }
          },
          orderBy: { startDate: 'asc' }
        }
      }
    });

    if (!desk) {
      throw new Error('Desk not found');
    }

    return desk;
  }

  static async updateDesk(id: string, data: UpdateDesk) {
    try {
      const desk = await prisma.desk.update({
        where: { id },
        data: {
          ...data,
          location: data.location as any
        }
      });
      return desk;
    } catch (error: any) {
      if (error.code === 'P2025') {
        throw new Error('Desk not found');
      }
      if (error.code === 'P2002') {
        throw new Error('A desk with this name already exists');
      }
      throw new Error(`Failed to update desk: ${error.message}`);
    }
  }

  static async deleteDesk(id: string) {
    try {
      // Check if desk has active bookings
      const activeBookings = await prisma.booking.count({
        where: {
          deskId: id,
          status: 'CONFIRMED',
          startDate: { gte: new Date() }
        }
      });

      if (activeBookings > 0) {
        throw new Error('Cannot delete desk with active bookings');
      }

      await prisma.desk.delete({
        where: { id }
      });

      return { success: true };
    } catch (error: any) {
      if (error.code === 'P2025') {
        throw new Error('Desk not found');
      }
      throw new Error(`Failed to delete desk: ${error.message}`);
    }
  }

  static async checkAvailability(id: string, query: CheckAvailabilityQuery) {
    const { date, userId } = query;
    const targetDate = new Date(date);
    const startOfDay = new Date(targetDate.setHours(0, 0, 0, 0));
    const endOfDay = new Date(targetDate.setHours(23, 59, 59, 999));

    const existingBooking = await prisma.booking.findFirst({
      where: {
        deskId: id,
        status: 'CONFIRMED',
        startDate: { gte: startOfDay },
        endDate: { lte: endOfDay },
        ...(userId && { userId: { not: userId } })
      },
      include: {
        user: {
          select: { name: true, email: true }
        }
      }
    });

    return {
      available: !existingBooking,
      conflict: existingBooking ? {
        bookingId: existingBooking.id,
        user: existingBooking.user,
        startDate: existingBooking.startDate,
        endDate: existingBooking.endDate
      } : null
    };
  }
}

'use client';

import { useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Separator } from '@/components/ui/separator';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import {
  ArrowLeft,
  Calendar,
  Clock,
  MapPin,
  Users,
  Building,
  Mail,
  Phone,
  CheckCircle,
  XCircle,
  AlertCircle,
  Edit,
  Trash2,
  UserCheck,
  UserX
} from 'lucide-react';
import Link from 'next/link';
import { useBooking, useCancelBooking, useCheckInBooking, useCheckOutBooking } from '@/lib/hooks/use-bookings';
import { DateTime } from 'luxon';
import { toast } from 'sonner';

export default function BookingDetailPage() {
  const params = useParams();
  const router = useRouter();
  const bookingId = params.id as string;

  const { data: booking, isLoading, error } = useBooking(bookingId);
  const cancelBooking = useCancelBooking();
  const checkInBooking = useCheckInBooking();
  const checkOutBooking = useCheckOutBooking();

  const [showCancelDialog, setShowCancelDialog] = useState(false);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
        <div className="max-w-4xl mx-auto p-6 space-y-6">
          <div className="flex items-center gap-4">
            <Skeleton className="h-10 w-10 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-8 w-64" />
              <Skeleton className="h-4 w-48" />
            </div>
          </div>
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardContent className="p-6 space-y-4">
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6 space-y-4">
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  if (error || !booking) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
        <div className="max-w-4xl mx-auto p-6">
          <div className="text-center py-12">
            <AlertCircle className="mx-auto h-12 w-12 text-red-500" />
            <h3 className="mt-4 text-lg font-semibold text-gray-900">Reserva no encontrada</h3>
            <p className="mt-2 text-gray-600">
              La reserva que buscas no existe o no tienes permisos para verla.
            </p>
            <Button asChild className="mt-4">
              <Link href="/dashboard/bookings">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Volver a Reservas
              </Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const formatDateTime = (date: Date | string) => {
    return DateTime.fromJSDate(new Date(date)).toFormat('dd/MM/yyyy HH:mm');
  };

  const formatTime = (date: Date | string) => {
    return DateTime.fromJSDate(new Date(date)).toFormat('HH:mm');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'CONFIRMED':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'COMPLETED':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'NO_SHOW':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'CONFIRMED':
        return <CheckCircle className="h-4 w-4" />;
      case 'COMPLETED':
        return <CheckCircle className="h-4 w-4" />;
      case 'CANCELLED':
        return <XCircle className="h-4 w-4" />;
      case 'NO_SHOW':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const handleCancel = async () => {
    try {
      await cancelBooking.mutateAsync(bookingId);
      setShowCancelDialog(false);
      toast.success('Reserva cancelada exitosamente');
    } catch (error) {
      toast.error('Error al cancelar la reserva');
    }
  };

  const handleCheckIn = async () => {
    try {
      await checkInBooking.mutateAsync(bookingId);
      toast.success('Check-in realizado exitosamente');
    } catch (error) {
      toast.error('Error al realizar check-in');
    }
  };

  const handleCheckOut = async () => {
    try {
      await checkOutBooking.mutateAsync(bookingId);
      toast.success('Check-out realizado exitosamente');
    } catch (error) {
      toast.error('Error al realizar check-out');
    }
  };

  const canCheckIn = booking.status === 'CONFIRMED' && !booking.checkIn;
  const canCheckOut = booking.status === 'CONFIRMED' && booking.checkIn && !booking.checkOut;
  const canCancel = booking.status === 'CONFIRMED';

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
      <div className="max-w-4xl mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="outline" size="icon" asChild className="rounded-full border-gray-300 text-gray-700 hover:bg-gray-50">
              <Link href="/dashboard/bookings">
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{booking.title}</h1>
              <p className="text-gray-600">Detalles de la reserva</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge className={`${getStatusColor(booking.status)} rounded-full px-3 py-1 text-sm font-medium border`}>
              <span className="flex items-center gap-1">
                {getStatusIcon(booking.status)}
                {booking.status}
              </span>
            </Badge>
          </div>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          {/* Booking Information */}
          <Card className="border-0 shadow-lg rounded-2xl bg-white/80 backdrop-blur-sm">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2 text-purple-900">
                <Calendar className="h-5 w-5" />
                Información de la Reserva
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Fecha y Hora</p>
                    <p className="font-medium">{formatDateTime(booking.startDate)} - {formatTime(booking.endDate)}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  {booking.resourceType === 'MEETING_ROOM' ? (
                    <Building className="h-4 w-4 text-gray-500" />
                  ) : (
                    <Users className="h-4 w-4 text-gray-500" />
                  )}
                  <div>
                    <p className="text-sm text-gray-500">
                      {booking.resourceType === 'MEETING_ROOM' ? 'Sala de Reunión' : 'Escritorio'}
                    </p>
                    <p className="font-medium">
                      {booking.resourceType === 'MEETING_ROOM' 
                        ? booking.meetingRoom?.name || 'Sala de Reunión'
                        : booking.desk?.name || 'Escritorio'
                      }
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <MapPin className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Ubicación</p>
                    <p className="font-medium">
                      {booking.resourceType === 'MEETING_ROOM' 
                        ? `${booking.meetingRoom?.location?.floor ? `Piso ${booking.meetingRoom.location.floor}` : ''} ${booking.meetingRoom?.location?.room || ''}`
                        : `${booking.desk?.location?.floor ? `Piso ${booking.desk.location.floor}` : ''} ${booking.desk?.location?.zone || ''}`
                      }
                    </p>
                  </div>
                </div>

                {booking.description && (
                  <div>
                    <p className="text-sm text-gray-500 mb-1">Descripción</p>
                    <p className="text-gray-700">{booking.description}</p>
                  </div>
                )}
              </div>

              <Separator />

              <div className="space-y-3">
                <h4 className="font-medium text-gray-900">Estado de la Reserva</h4>
                
                {booking.checkIn && (
                  <div className="flex items-center gap-3 text-green-700">
                    <UserCheck className="h-4 w-4" />
                    <div>
                      <p className="text-sm">Check-in realizado</p>
                      <p className="text-xs text-green-600">
                        {formatDateTime(booking.checkIn.time)}
                      </p>
                    </div>
                  </div>
                )}

                {booking.checkOut && (
                  <div className="flex items-center gap-3 text-blue-700">
                    <UserX className="h-4 w-4" />
                    <div>
                      <p className="text-sm">Check-out realizado</p>
                      <p className="text-xs text-blue-600">
                        {formatDateTime(booking.checkOut.time)}
                      </p>
                    </div>
                  </div>
                )}

                {booking.status === 'CANCELLED' && booking.cancellationReason && (
                  <div className="p-3 bg-red-50 rounded-lg border border-red-200">
                    <p className="text-sm text-red-800">
                      <strong>Motivo de cancelación:</strong> {booking.cancellationReason}
                    </p>
                    {booking.cancelledAt && (
                      <p className="text-xs text-red-600 mt-1">
                        Cancelado el {formatDateTime(booking.cancelledAt)}
                      </p>
                    )}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Attendees and Actions */}
          <div className="space-y-6">
            {/* Attendees */}
            {(booking.attendees?.internal?.length > 0 || booking.attendees?.external?.length > 0) && (
              <Card className="border-0 shadow-lg rounded-2xl bg-white/80 backdrop-blur-sm">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-2 text-purple-900">
                    <Users className="h-5 w-5" />
                    Asistentes
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {booking.attendees?.internal && booking.attendees.internal.length > 0 && (
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Empleados</h4>
                      <div className="space-y-2">
                        {booking.attendees.internal.map((attendee: string, index: number) => (
                          <div key={index} className="flex items-center gap-2 text-sm">
                            <Mail className="h-4 w-4 text-blue-500" />
                            <span>{attendee}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {booking.attendees?.external && booking.attendees.external.length > 0 && (
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Invitados Externos</h4>
                      <div className="space-y-2">
                        {booking.attendees.external.map((attendee: string, index: number) => (
                          <div key={index} className="flex items-center gap-2 text-sm">
                            <Mail className="h-4 w-4 text-green-500" />
                            <span>{attendee}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Actions */}
            <Card className="border-0 shadow-lg rounded-2xl bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-purple-900">Acciones</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {canCheckIn && (
                  <Button 
                    onClick={handleCheckIn}
                    className="w-full bg-green-600 hover:bg-green-700 rounded-xl"
                    disabled={checkInBooking.isPending}
                  >
                    <UserCheck className="mr-2 h-4 w-4" />
                    {checkInBooking.isPending ? 'Realizando Check-in...' : 'Realizar Check-in'}
                  </Button>
                )}

                {canCheckOut && (
                  <Button 
                    onClick={handleCheckOut}
                    className="w-full bg-blue-600 hover:bg-blue-700 rounded-xl"
                    disabled={checkOutBooking.isPending}
                  >
                    <UserX className="mr-2 h-4 w-4" />
                    {checkOutBooking.isPending ? 'Realizando Check-out...' : 'Realizar Check-out'}
                  </Button>
                )}

                <Button variant="outline" className="w-full rounded-xl border-gray-300 text-gray-700 hover:bg-gray-50">
                  <Edit className="mr-2 h-4 w-4" />
                  Editar Reserva
                </Button>

                {canCancel && (
                  <AlertDialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
                    <AlertDialogTrigger asChild>
                      <Button variant="outline" className="w-full rounded-xl border-red-300 text-red-700 hover:bg-red-50">
                        <Trash2 className="mr-2 h-4 w-4" />
                        Cancelar Reserva
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>¿Cancelar reserva?</AlertDialogTitle>
                        <AlertDialogDescription>
                          Esta acción no se puede deshacer. La reserva será cancelada y se notificará a todos los asistentes.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Mantener Reserva</AlertDialogCancel>
                        <AlertDialogAction 
                          onClick={handleCancel}
                          className="bg-red-600 hover:bg-red-700"
                          disabled={cancelBooking.isPending}
                        >
                          {cancelBooking.isPending ? 'Cancelando...' : 'Sí, Cancelar'}
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}

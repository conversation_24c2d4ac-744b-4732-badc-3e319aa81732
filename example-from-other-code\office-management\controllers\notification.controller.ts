import { Request, Response } from 'express';
import { validationResult } from 'express-validator';
import { NotificationService } from '../services/notification.service';
import { OfficeUserModel } from '../models/user.model';
import { BookingModel } from '../models/booking.model';
import { GuestModel } from '../models/guest.model';
import { MeetingRoomModel } from '../models/meeting-room.model';

export class NotificationController {
  private notificationService: NotificationService;

  constructor() {
    this.notificationService = new NotificationService();
  }

  // Send custom notification to user
  async sendCustomNotification(req: Request, res: Response) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation errors',
          errors: errors.array()
        });
      }

      const { userId, subject, message, channels = ['email'] } = req.body;

      const user = await OfficeUserModel.findById(userId);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      const results = await this.notificationService.sendCustomNotification(
        user,
        subject,
        message,
        channels
      );

      res.json({
        success: true,
        message: 'Notification sent',
        data: results
      });
    } catch (error) {
      console.error('Error sending custom notification:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Send booking reminder
  async sendBookingReminder(req: Request, res: Response) {
    try {
      const { bookingId } = req.params;
      const { channels = ['email', 'slack'] } = req.body;

      const booking = await BookingModel.findById(bookingId)
        .populate('userId')
        .populate('resourceId');

      if (!booking) {
        return res.status(404).json({
          success: false,
          message: 'Booking not found'
        });
      }

      const user = await OfficeUserModel.findById(booking.userId);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      let meetingRoom = null;
      if (booking.resourceType === 'meeting-room') {
        meetingRoom = await MeetingRoomModel.findById(booking.resourceId);
      }

      const results = await this.notificationService.sendBookingReminder(
        booking,
        user,
        meetingRoom
      );

      res.json({
        success: true,
        message: 'Booking reminder sent',
        data: results
      });
    } catch (error) {
      console.error('Error sending booking reminder:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Send guest invitation
  async sendGuestInvitation(req: Request, res: Response) {
    try {
      const { guestId } = req.params;
      const { channels = ['email'] } = req.body;

      const guest = await GuestModel.findById(guestId)
        .populate('hostUserId');

      if (!guest) {
        return res.status(404).json({
          success: false,
          message: 'Guest not found'
        });
      }

      const host = await OfficeUserModel.findById(guest.hostUserId);
      if (!host) {
        return res.status(404).json({
          success: false,
          message: 'Host not found'
        });
      }

      const results = await this.notificationService.sendGuestInvitation(guest, host);

      res.json({
        success: true,
        message: 'Guest invitation sent',
        data: results
      });
    } catch (error) {
      console.error('Error sending guest invitation:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Bulk send notifications
  async sendBulkNotifications(req: Request, res: Response) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation errors',
          errors: errors.array()
        });
      }

      const { userIds, subject, message, channels = ['email'] } = req.body;

      const users = await OfficeUserModel.find({
        _id: { $in: userIds },
        isActive: true
      });

      if (users.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'No active users found'
        });
      }

      const results = [];
      for (const user of users) {
        try {
          const result = await this.notificationService.sendCustomNotification(
            user,
            subject,
            message,
            channels
          );
          results.push({
            userId: user._id,
            userName: `${user.firstName} ${user.lastName}`,
            result
          });
        } catch (error) {
          results.push({
            userId: user._id,
            userName: `${user.firstName} ${user.lastName}`,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      res.json({
        success: true,
        message: 'Bulk notifications processed',
        data: results
      });
    } catch (error) {
      console.error('Error sending bulk notifications:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Send department-wide notification
  async sendDepartmentNotification(req: Request, res: Response) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation errors',
          errors: errors.array()
        });
      }

      const { department, subject, message, channels = ['email'] } = req.body;

      const users = await OfficeUserModel.find({
        department,
        isActive: true
      });

      if (users.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'No active users found in department'
        });
      }

      const results = [];
      for (const user of users) {
        try {
          const result = await this.notificationService.sendCustomNotification(
            user,
            subject,
            message,
            channels
          );
          results.push({
            userId: user._id,
            userName: `${user.firstName} ${user.lastName}`,
            result
          });
        } catch (error) {
          results.push({
            userId: user._id,
            userName: `${user.firstName} ${user.lastName}`,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      res.json({
        success: true,
        message: 'Department notifications sent',
        data: {
          department,
          totalUsers: users.length,
          results
        }
      });
    } catch (error) {
      console.error('Error sending department notification:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Get notification status
  async getNotificationStatus(req: Request, res: Response) {
    try {
      const { type, id } = req.params;

      let entity;
      switch (type) {
        case 'booking':
          entity = await BookingModel.findById(id).select('notifications');
          break;
        case 'guest':
          entity = await GuestModel.findById(id).select('notifications');
          break;
        default:
          return res.status(400).json({
            success: false,
            message: 'Invalid notification type'
          });
      }

      if (!entity) {
        return res.status(404).json({
          success: false,
          message: `${type} not found`
        });
      }

      res.json({
        success: true,
        data: entity.notifications
      });
    } catch (error) {
      console.error('Error getting notification status:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Test notification services
  async testNotificationServices(req: Request, res: Response) {
    try {
      const { email, slackUserId, phone } = req.body;
      const results: any = {};

      // Test email service
      if (email) {
        try {
          const testUser = {
            firstName: 'Test',
            lastName: 'User',
            email,
            preferences: {
              notificationSettings: {
                email: true,
                slack: false,
                whatsapp: false
              }
            }
          };

          const emailResult = await this.notificationService.sendCustomNotification(
            testUser as any,
            'Test Email Notification',
            'This is a test email notification from the office management system.',
            ['email']
          );

          results.email = emailResult;
        } catch (error) {
          results.email = {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          };
        }
      }

      // Test Slack service
      if (slackUserId) {
        try {
          const testUser = {
            firstName: 'Test',
            lastName: 'User',
            email: '<EMAIL>',
            slackUserId,
            preferences: {
              notificationSettings: {
                email: false,
                slack: true,
                whatsapp: false
              }
            }
          };

          const slackResult = await this.notificationService.sendCustomNotification(
            testUser as any,
            'Test Slack Notification',
            'This is a test Slack notification from the office management system.',
            ['slack']
          );

          results.slack = slackResult;
        } catch (error) {
          results.slack = {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          };
        }
      }

      res.json({
        success: true,
        message: 'Notification services tested',
        data: results
      });
    } catch (error) {
      console.error('Error testing notification services:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
}

import { Router } from 'express';
import {
  getAllDesks,
  getDeskById,
  createDesk,
  updateDesk,
  deleteDesk,
  checkAvailability,
  getAvailabilityCalendar,
  getDesksWithAvailability,
  getAvailableDesks
} from '../controllers/desks.controller';
import { verifyTokenOfficeManagement, verifyAdminRole } from '../../../middlewares/verification-token';

const desksRouter = Router();

// Public routes (authenticated users)
desksRouter.get('/desks', verifyTokenOfficeManagement, getAllDesks);
desksRouter.get('/desks/availability', verifyTokenOfficeManagement, getDesksWithAvailability);
desksRouter.get('/desks/available', verifyTokenOfficeManagement, getAvailableDesks);
desksRouter.get('/desks/:id', verifyTokenOfficeManagement, getDeskById);
desksRouter.get('/desks/:id/calendar', verifyTokenOfficeManagement, getAvailabilityCalendar);
desksRouter.post('/desks/:id/check-availability', verifyTokenOfficeManagement, checkAvailability);

// Admin only routes
desksRouter.post('/desks', verifyTokenOfficeManagement, verifyAdminRole, createDesk);
desksRouter.put('/desks/:id', verifyTokenOfficeManagement, verifyAdminRole, updateDesk);
desksRouter.delete('/desks/:id', verifyTokenOfficeManagement, verifyAdminRole, deleteDesk);

export default desksRouter;

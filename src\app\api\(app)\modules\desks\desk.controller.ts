import { Elysia } from 'elysia';
import { DeskService } from './desk.service';
import {
  createDesk,
  updateDesk,
  getDeskQuery,
  deskParams,
  checkAvailabilityQuery
} from './desks.validators';
import { authMiddleware } from '../../middlewares/auth.middleware';

export const desksController = new Elysia({
  prefix: '/desks',
  detail: {
    tags: ['Desks'],
    description: 'Endpoints for managing desks'
  }
})
  .use(authMiddleware)

  // Get all desks
  .get('/', async ({ query }) => {
    try {
      const result = await DeskService.getAllDesks(query);
      return {
        success: true,
        data: result.data,
        pagination: result.pagination
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }, {
    query: getDeskQuery
  })

  // Create new desk
  .post('/', async ({ body }) => {
    try {
      const desk = await DeskService.createDesk(body);
      return {
        success: true,
        data: desk
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }, {
    body: createDesk
  })

  // Get desk by ID
  .get('/:id', async ({ params }) => {
    try {
      const desk = await DeskService.getDeskById(params.id);
      return {
        success: true,
        data: desk
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }, {
    params: deskParams
  })

  // Update desk
  .put('/:id', async ({ params, body }) => {
    try {
      const desk = await DeskService.updateDesk(params.id, body);
      return {
        success: true,
        data: desk
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }, {
    params: deskParams,
    body: updateDesk
  })

  // Delete desk
  .delete('/:id', async ({ params }) => {
    try {
      const result = await DeskService.deleteDesk(params.id);
      return {
        success: true,
        message: 'Desk deleted successfully',
        data: result
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }, {
    params: deskParams
  })

  // Check desk availability
  .get('/:id/availability', async ({ params, query }) => {
    try {
      const availability = await DeskService.checkAvailability(params.id, query);
      return {
        success: true,
        data: availability
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }, {
    params: deskParams,
    query: checkAvailabilityQuery
  });

### Office Management - Bookings API Tests

@baseUrl = http://localhost:3000
@authToken = your-jwt-token-here

### Create a new booking
POST {{baseUrl}}/api/office-management/bookings
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "resourceType": "meeting-room",
  "resourceId": "60f7b3b3b3b3b3b3b3b3b3b3",
  "title": "Team Standup",
  "description": "Daily team standup meeting",
  "startDate": "2024-01-15T09:00:00.000Z",
  "endDate": "2024-01-15T09:30:00.000Z",
  "attendees": {
    "internal": ["60f7b3b3b3b3b3b3b3b3b3b4"],
    "external": ["<EMAIL>"]
  }
}

### Get all bookings for current user
GET {{baseUrl}}/api/office-management/bookings
Authorization: Bearer {{authToken}}

### Get bookings with filters
GET {{baseUrl}}/api/office-management/bookings?startDate=2024-01-15&endDate=2024-01-16&status=confirmed
Authorization: Bearer {{authToken}}

### Get specific booking
GET {{baseUrl}}/api/office-management/bookings/60f7b3b3b3b3b3b3b3b3b3b5
Authorization: Bearer {{authToken}}

### Update booking
PUT {{baseUrl}}/api/office-management/bookings/60f7b3b3b3b3b3b3b3b3b3b5
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "title": "Updated Team Standup",
  "description": "Updated daily team standup meeting",
  "endDate": "2024-01-15T10:00:00.000Z"
}

### Cancel booking
PATCH {{baseUrl}}/api/office-management/bookings/60f7b3b3b3b3b3b3b3b3b3b5/cancel
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "cancellationReason": "Meeting postponed"
}

### Check-in to booking
PATCH {{baseUrl}}/api/office-management/bookings/60f7b3b3b3b3b3b3b3b3b3b5/checkin
Authorization: Bearer {{authToken}}

### Check-out from booking
PATCH {{baseUrl}}/api/office-management/bookings/60f7b3b3b3b3b3b3b3b3b3b5/checkout
Authorization: Bearer {{authToken}}

### Delete booking
DELETE {{baseUrl}}/api/office-management/bookings/60f7b3b3b3b3b3b3b3b3b3b5
Authorization: Bearer {{authToken}}

### Get booking conflicts for a room
GET {{baseUrl}}/api/office-management/bookings/conflicts?resourceType=meeting-room&resourceId=60f7b3b3b3b3b3b3b3b3b3b3&startDate=2024-01-15T09:00:00.000Z&endDate=2024-01-15T10:00:00.000Z
Authorization: Bearer {{authToken}}

### Get user's upcoming bookings
GET {{baseUrl}}/api/office-management/bookings/upcoming
Authorization: Bearer {{authToken}}

### Get booking statistics
GET {{baseUrl}}/api/office-management/bookings/stats?period=week
Authorization: Bearer {{authToken}}

### Bulk create bookings (recurring meetings)
POST {{baseUrl}}/api/office-management/bookings/bulk
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "resourceType": "meeting-room",
  "resourceId": "60f7b3b3b3b3b3b3b3b3b3b3",
  "title": "Weekly Team Meeting",
  "description": "Weekly team sync",
  "startTime": "09:00",
  "endTime": "10:00",
  "recurrence": {
    "type": "weekly",
    "daysOfWeek": [1, 3, 5],
    "startDate": "2024-01-15",
    "endDate": "2024-03-15"
  },
  "attendees": {
    "internal": ["60f7b3b3b3b3b3b3b3b3b3b4"]
  }
}

### Export bookings to calendar
GET {{baseUrl}}/api/office-management/bookings/export/calendar?format=ics&startDate=2024-01-01&endDate=2024-01-31
Authorization: Bearer {{authToken}}

### Send booking reminder
POST {{baseUrl}}/api/office-management/bookings/60f7b3b3b3b3b3b3b3b3b3b5/remind
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "channels": ["email", "slack"]
}

import { Router } from 'express';
import { GuestController } from '../controllers/guest.controller';
import { GuestValidator } from '../validators/guest.validator';
import { verifyToken } from '../../middlewares/verifyToken';

const router = Router();
const guestController = new GuestController();

// Apply authentication middleware to all routes
router.use(verifyToken);

// Guest management routes
router.post('/', 
  GuestValidator.createGuest,
  guestController.createGuest.bind(guestController)
);

router.get('/', 
  GuestValidator.getGuests,
  guestController.getGuests.bind(guestController)
);

router.get('/today', 
  guestController.getTodaysGuests.bind(guestController)
);

router.get('/:id', 
  GuestValidator.getGuestById,
  guestController.getGuestById.bind(guestController)
);

router.put('/:id', 
  GuestValidator.updateGuest,
  guestController.updateGuest.bind(guestController)
);

router.patch('/:id/confirm', 
  GuestValidator.getGuestById,
  guestController.confirmGuest.bind(guestController)
);

router.patch('/:id/checkin', 
  GuestValidator.checkInGuest,
  guestController.checkInGuest.bind(guestController)
);

router.patch('/:id/checkout', 
  GuestValidator.checkOutGuest,
  guestController.checkOutGuest.bind(guestController)
);

router.patch('/:id/cancel', 
  GuestValidator.cancelGuest,
  guestController.cancelGuest.bind(guestController)
);

router.post('/:id/invite', 
  GuestValidator.sendInvitation,
  guestController.sendInvitation.bind(guestController)
);

router.delete('/:id', 
  GuestValidator.getGuestById,
  guestController.deleteGuest.bind(guestController)
);

export default router;

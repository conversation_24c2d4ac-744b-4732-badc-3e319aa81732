import { prisma } from '@/db';
import { CreateGuest, UpdateGuest, GetGuestsQuery, BulkGuestOperation, GetDailyGuestsQuery } from './guests.validators';

export class GuestService {
  static async createGuest(data: CreateGuest, hostId: string) {
    try {
      const guest = await prisma.guest.create({
        data: {
          ...data,
          hostId,
          visitDate: new Date(data.visitDate),
          notifications: {
            email: { sent: false },
            whatsapp: { sent: false }
          }
        },
        include: {
          host: {
            select: { id: true, name: true, email: true }
          },
          booking: {
            include: {
              meetingRoom: true,
              desk: true
            }
          }
        }
      });

      return guest;
    } catch (error: any) {
      throw new Error(`Failed to create guest: ${error.message}`);
    }
  }

  static async getAllGuests(query: GetGuestsQuery = {}) {
    const {
      page = 1,
      limit = 10,
      startDate,
      endDate,
      status,
      hostId,
      visitDate,
      search
    } = query;

    const skip = (page - 1) * limit;
    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { company: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (startDate || endDate) {
      where.visitDate = {};
      if (startDate) where.visitDate.gte = new Date(startDate);
      if (endDate) where.visitDate.lte = new Date(endDate);
    }

    if (visitDate) {
      const date = new Date(visitDate);
      const startOfDay = new Date(date);
      startOfDay.setHours(0, 0, 0, 0);
      const endOfDay = new Date(date);
      endOfDay.setHours(23, 59, 59, 999);

      where.visitDate = {
        gte: startOfDay,
        lte: endOfDay
      };
    }

    if (status) {
      where.status = status;
    }

    if (hostId) {
      where.hostId = hostId;
    }

    const [guests, total] = await Promise.all([
      prisma.guest.findMany({
        where,
        skip,
        take: limit,
        include: {
          host: {
            select: { id: true, name: true, email: true }
          },
          booking: {
            include: {
              meetingRoom: true,
              desk: true
            }
          }
        },
        orderBy: { visitDate: 'desc' }
      }),
      prisma.guest.count({ where })
    ]);

    return {
      data: guests,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  static async getGuestById(id: string, hostId?: string) {
    const where: any = { id };

    // If hostId is provided, ensure host can only see their own guests
    if (hostId) {
      where.hostId = hostId;
    }

    const guest = await prisma.guest.findFirst({
      where,
      include: {
        host: {
          select: { id: true, name: true, email: true }
        },
        booking: {
          include: {
            meetingRoom: true,
            desk: true,
            user: {
              select: { id: true, name: true, email: true }
            }
          }
        }
      }
    });

    if (!guest) {
      throw new Error('Guest not found');
    }

    return guest;
  }

  static async updateGuest(id: string, data: UpdateGuest, hostId?: string) {
    const guest = await this.getGuestById(id, hostId);

    // Only the host can update their guests
    if (hostId && guest.hostId !== hostId) {
      throw new Error('You can only update your own guests');
    }

    // Don't allow updating past visits
    if (guest.visitDate < new Date() && guest.status !== 'PENDING') {
      throw new Error('Cannot update past visits');
    }

    try {
      const updatedGuest = await prisma.guest.update({
        where: { id },
        data: {
          ...data,
          visitDate: data.visitDate ? new Date(data.visitDate) : undefined
        },
        include: {
          host: {
            select: { id: true, name: true, email: true }
          },
          booking: {
            include: {
              meetingRoom: true,
              desk: true
            }
          }
        }
      });

      return updatedGuest;
    } catch (error: any) {
      throw new Error(`Failed to update guest: ${error.message}`);
    }
  }

  static async deleteGuest(id: string, hostId?: string) {
    const guest = await this.getGuestById(id, hostId);

    // Only the host can delete their guests
    if (hostId && guest.hostId !== hostId) {
      throw new Error('You can only delete your own guests');
    }

    // Don't allow deleting checked-in guests
    if (guest.status === 'CHECKED_IN') {
      throw new Error('Cannot delete checked-in guests. Please check them out first.');
    }

    try {
      await prisma.guest.delete({
        where: { id }
      });

      return { success: true };
    } catch (error: any) {
      throw new Error(`Failed to delete guest: ${error.message}`);
    }
  }

  static async checkInGuest(id: string) {
    const guest = await this.getGuestById(id);

    if (guest.status !== 'PENDING') {
      throw new Error('Only pending guests can be checked in');
    }

    const now = new Date();
    const visitDate = new Date(guest.visitDate);

    // Check if it's the correct visit date
    if (visitDate.toDateString() !== now.toDateString()) {
      throw new Error('Guest can only be checked in on their visit date');
    }

    const updatedGuest = await prisma.guest.update({
      where: { id },
      data: {
        status: 'CHECKED_IN',
        checkInTime: now
      },
      include: {
        host: {
          select: { id: true, name: true, email: true }
        },
        booking: {
          include: {
            meetingRoom: true,
            desk: true
          }
        }
      }
    });

    return updatedGuest;
  }

  static async checkOutGuest(id: string) {
    const guest = await this.getGuestById(id);

    if (guest.status !== 'CHECKED_IN') {
      throw new Error('Only checked-in guests can be checked out');
    }

    const updatedGuest = await prisma.guest.update({
      where: { id },
      data: {
        status: 'CHECKED_OUT',
        checkOutTime: new Date()
      },
      include: {
        host: {
          select: { id: true, name: true, email: true }
        },
        booking: {
          include: {
            meetingRoom: true,
            desk: true
          }
        }
      }
    });

    return updatedGuest;
  }

  static async markNoShow(id: string) {
    const guest = await this.getGuestById(id);

    if (guest.status !== 'PENDING') {
      throw new Error('Only pending guests can be marked as no-show');
    }

    const updatedGuest = await prisma.guest.update({
      where: { id },
      data: {
        status: 'NO_SHOW'
      },
      include: {
        host: {
          select: { id: true, name: true, email: true }
        },
        booking: {
          include: {
            meetingRoom: true,
            desk: true
          }
        }
      }
    });

    return updatedGuest;
  }

  static async bulkOperation(data: BulkGuestOperation) {
    const { guestIds, operation, reason } = data;

    const guests = await prisma.guest.findMany({
      where: {
        id: { in: guestIds }
      }
    });

    if (guests.length !== guestIds.length) {
      throw new Error('Some guests were not found');
    }

    let updateData: any = {};

    switch (operation) {
      case 'check-in':
        updateData = {
          status: 'CHECKED_IN',
          checkInTime: new Date()
        };
        break;
      case 'check-out':
        updateData = {
          status: 'CHECKED_OUT',
          checkOutTime: new Date()
        };
        break;
      case 'mark-no-show':
        updateData = {
          status: 'NO_SHOW'
        };
        break;
      case 'delete':
        await prisma.guest.deleteMany({
          where: {
            id: { in: guestIds },
            status: { not: 'CHECKED_IN' }
          }
        });
        return { success: true, message: `Deleted ${guestIds.length} guests` };
    }

    const result = await prisma.guest.updateMany({
      where: {
        id: { in: guestIds }
      },
      data: updateData
    });

    return { success: true, updated: result.count };
  }

  static async getDailyGuests(query: GetDailyGuestsQuery = {}) {
    const { date, status, hostId } = query;

    const targetDate = date ? new Date(date) : new Date();
    const startOfDay = new Date(targetDate);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(targetDate);
    endOfDay.setHours(23, 59, 59, 999);

    const where: any = {
      visitDate: {
        gte: startOfDay,
        lte: endOfDay
      }
    };

    if (status) {
      where.status = status;
    }

    if (hostId) {
      where.hostId = hostId;
    }

    const guests = await prisma.guest.findMany({
      where,
      include: {
        host: {
          select: { id: true, name: true, email: true }
        },
        booking: {
          include: {
            meetingRoom: true,
            desk: true
          }
        }
      },
      orderBy: { visitTime: 'asc' }
    });

    return guests;
  }
}
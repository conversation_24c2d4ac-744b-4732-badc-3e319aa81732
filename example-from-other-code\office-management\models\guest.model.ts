import mongoose, { Schema, Document } from 'mongoose';
import officeManagementDB from '../db';

export interface IGuest extends Document {
  name: string;
  email?: string;
  phone?: string;
  company?: string;
  visitDate: Date;
  visitTime: {
    start: string; // "14:30"
    end?: string;  // "16:00"
  };
  hostUserId: mongoose.Types.ObjectId;
  bookingId?: mongoose.Types.ObjectId; // Optional reference to booking
  purpose: string;
  status: 'invited' | 'confirmed' | 'checked-in' | 'checked-out' | 'no-show' | 'cancelled';
  notifications: {
    email: {
      sent: boolean;
      sentAt?: Date;
      confirmationToken?: string;
    };
    whatsapp: {
      sent: boolean;
      sentAt?: Date;
      messageId?: string;
    };
    slack: {
      hostNotified: boolean;
      notifiedAt?: Date;
    };
  };
  checkIn?: {
    time: Date;
    by: mongoose.Types.ObjectId; // Receptionist who checked them in
    notes?: string;
  };
  checkOut?: {
    time: Date;
    by: mongoose.Types.ObjectId; // Receptionist who checked them out
    notes?: string;
  };
  visitInstructions?: string;
  identificationRequired: boolean;
  identificationNumber?: string;
  emergencyContact?: {
    name: string;
    phone: string;
    relationship: string;
  };
  accessLevel: 'lobby' | 'floor' | 'full';
  specialRequirements?: string[];
  createdAt: Date;
  updatedAt: Date;
}

const GuestSchema = new Schema<IGuest>({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  email: {
    type: String,
    trim: true,
    lowercase: true,
    validate: {
      validator: function (email: string) {
        return !email || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
      },
      message: 'Invalid email format'
    }
  },
  phone: {
    type: String,
    trim: true,
    validate: {
      validator: function (phone: string) {
        return !phone || /^\+?[\d\s\-\(\)]+$/.test(phone);
      },
      message: 'Invalid phone format'
    }
  },
  company: {
    type: String,
    trim: true,
    maxlength: 100
  },
  visitDate: {
    type: Date,
    required: true
  },
  visitTime: {
    start: {
      type: String,
      required: true,
      validate: {
        validator: function (time: string) {
          return /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(time);
        },
        message: 'Invalid time format. Use HH:MM'
      }
    },
    end: {
      type: String,
      validate: {
        validator: function (time: string) {
          return !time || /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(time);
        },
        message: 'Invalid time format. Use HH:MM'
      }
    }
  },
  hostUserId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  bookingId: {
    type: Schema.Types.ObjectId,
    ref: 'Booking'
  },
  purpose: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  status: {
    type: String,
    enum: ['invited', 'confirmed', 'checked-in', 'checked-out', 'no-show', 'cancelled'],
    default: 'invited'
  },
  notifications: {
    email: {
      sent: {
        type: Boolean,
        default: false
      },
      sentAt: Date,
      confirmationToken: String
    },
    whatsapp: {
      sent: {
        type: Boolean,
        default: false
      },
      sentAt: Date,
      messageId: String
    },
    slack: {
      hostNotified: {
        type: Boolean,
        default: false
      },
      notifiedAt: Date
    }
  },
  checkIn: {
    time: Date,
    by: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    notes: {
      type: String,
      maxlength: 500
    }
  },
  checkOut: {
    time: Date,
    by: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    notes: {
      type: String,
      maxlength: 500
    }
  },
  visitInstructions: {
    type: String,
    trim: true,
    maxlength: 500
  },
  identificationRequired: {
    type: Boolean,
    default: true
  },
  identificationNumber: {
    type: String,
    trim: true
  },
  emergencyContact: {
    name: {
      type: String,
      trim: true,
      maxlength: 100
    },
    phone: {
      type: String,
      trim: true
    },
    relationship: {
      type: String,
      trim: true,
      maxlength: 50
    }
  },
  accessLevel: {
    type: String,
    enum: ['lobby', 'floor', 'full'],
    default: 'lobby'
  },
  specialRequirements: [{
    type: String,
    trim: true
  }]
}, {
  timestamps: true,
  toJSON: {
    transform: function (doc, ret) {
      delete ret.__v;
      return ret;
    }
  }
});

// Validation: Either email or phone must be provided
GuestSchema.pre('validate', function (this: IGuest) {
  if (!this.email && !this.phone) {
    this.invalidate('contact', 'Either email or phone number must be provided');
  }
});

// Indexes
GuestSchema.index({ hostUserId: 1 });
GuestSchema.index({ visitDate: 1 });
GuestSchema.index({ status: 1 });
GuestSchema.index({ visitDate: 1, status: 1 });
GuestSchema.index({ email: 1 });
GuestSchema.index({ phone: 1 });

export const GuestModel = officeManagementDB.model<IGuest>('Guest', GuestSchema);

'use client';

import { useState } from 'react';
import { useParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Building, 
  Users, 
  MapPin, 
  Calendar,
  Clock,
  ArrowLeft,
  Wifi,
  Monitor,
  Coffee,
  CheckCircle,
  XCircle
} from 'lucide-react';
import Link from 'next/link';
import { useMeetingRoom, useMeetingRoomBookings } from '@/lib/hooks/use-meeting-rooms';
import { DateTime } from 'luxon';

export default function MeetingRoomDetailPage() {
  const params = useParams();
  const roomId = params.id as string;

  const { data: room, isLoading: loadingRoom, error } = useMeetingRoom(roomId);
  const { data: bookings, isLoading: loadingBookings } = useMeetingRoomBookings(roomId);

  const getAmenityIcon = (amenity: string) => {
    switch (amenity.toLowerCase()) {
      case 'wifi':
        return <Wifi className="h-4 w-4" />;
      case 'projector':
      case 'tv':
      case 'monitor':
        return <Monitor className="h-4 w-4" />;
      case 'coffee':
        return <Coffee className="h-4 w-4" />;
      default:
        return <Building className="h-4 w-4" />;
    }
  };

  const formatDateTime = (date: Date | string) => {
    return DateTime.fromJSDate(new Date(date)).toFormat('MMM dd, HH:mm');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'CONFIRMED':
        return 'bg-blue-100 text-blue-800';
      case 'COMPLETED':
        return 'bg-green-100 text-green-800';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800';
      case 'NO_SHOW':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (error) {
    return (
      <div className="flex flex-col gap-6 p-6">
        <div className="text-center py-12">
          <Building className="mx-auto h-12 w-12 text-muted-foreground" />
          <h3 className="mt-2 text-lg font-semibold">Room not found</h3>
          <p className="mt-1 text-sm text-muted-foreground">
            The meeting room you're looking for doesn't exist.
          </p>
          <Button className="mt-4" asChild>
            <Link href="/dashboard/rooms">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Rooms
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  if (loadingRoom) {
    return (
      <div className="flex flex-col gap-6 p-6">
        <div className="flex items-center gap-4">
          <Skeleton className="h-10 w-10" />
          <div className="space-y-2">
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-48" />
          </div>
        </div>
        <div className="grid gap-6 md:grid-cols-3">
          <div className="md:col-span-2 space-y-6">
            <Skeleton className="h-64 w-full" />
            <Skeleton className="h-32 w-full" />
          </div>
          <div className="space-y-6">
            <Skeleton className="h-48 w-full" />
            <Skeleton className="h-32 w-full" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-6 p-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" size="icon" asChild>
          <Link href="/dashboard/rooms">
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <div className="flex-1">
          <div className="flex items-center gap-3">
            <h1 className="text-3xl font-bold tracking-tight">{room?.data?.name}</h1>
            <Badge variant={room?.data?.isActive ? "default" : "secondary"}>
              {room?.data?.isActive ? "Active" : "Inactive"}
            </Badge>
          </div>
          <div className="flex items-center gap-2 text-muted-foreground">
            <MapPin className="h-4 w-4" />
            <span>
              {room?.data?.location?.building && `${room.data.location.building}, `}
              Floor {room?.data?.location?.floor}, {room?.data?.location?.room}
            </span>
          </div>
        </div>
        <div className="flex gap-2">
          <Button asChild>
            <Link href={`/dashboard/bookings/new?roomId=${roomId}`}>
              <Calendar className="mr-2 h-4 w-4" />
              Book This Room
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {/* Main Content */}
        <div className="md:col-span-2">
          <Tabs defaultValue="overview" className="space-y-6">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="bookings">Bookings</TabsTrigger>
              <TabsTrigger value="availability">Availability</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              {/* Description */}
              {room?.data?.description && (
                <Card>
                  <CardHeader>
                    <CardTitle>Description</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">{room.data.description}</p>
                  </CardContent>
                </Card>
              )}

              {/* Amenities & Equipment */}
              <div className="grid gap-6 md:grid-cols-2">
                {room?.data?.amenities?.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Amenities</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid gap-2">
                        {room.data.amenities.map((amenity: string) => (
                          <div key={amenity} className="flex items-center gap-2">
                            {getAmenityIcon(amenity)}
                            <span className="text-sm">{amenity}</span>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {room?.data?.equipment?.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Equipment</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid gap-2">
                        {room.data.equipment.map((item: string) => (
                          <div key={item} className="flex items-center gap-2">
                            <Monitor className="h-4 w-4" />
                            <span className="text-sm">{item}</span>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>

              {/* Booking Rules */}
              {room?.data?.bookingRules && (
                <Card>
                  <CardHeader>
                    <CardTitle>Booking Rules</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-4 md:grid-cols-2">
                      {room.data.bookingRules.minBookingDuration && (
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">Min Duration</span>
                          <span className="text-sm font-medium">
                            {room.data.bookingRules.minBookingDuration} minutes
                          </span>
                        </div>
                      )}
                      {room.data.bookingRules.maxBookingDuration && (
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">Max Duration</span>
                          <span className="text-sm font-medium">
                            {room.data.bookingRules.maxBookingDuration} minutes
                          </span>
                        </div>
                      )}
                      {room.data.bookingRules.advanceBookingDays && (
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">Advance Booking</span>
                          <span className="text-sm font-medium">
                            {room.data.bookingRules.advanceBookingDays} days
                          </span>
                        </div>
                      )}
                      {room.data.bookingRules.minNoticeHours && (
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">Min Notice</span>
                          <span className="text-sm font-medium">
                            {room.data.bookingRules.minNoticeHours} hours
                          </span>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="bookings" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Recent Bookings</CardTitle>
                  <CardDescription>
                    Latest reservations for this room
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {loadingBookings ? (
                    <div className="space-y-3">
                      {[...Array(3)].map((_, i) => (
                        <div key={i} className="flex items-center space-x-4">
                          <Skeleton className="h-12 w-12 rounded" />
                          <div className="space-y-2">
                            <Skeleton className="h-4 w-[200px]" />
                            <Skeleton className="h-4 w-[160px]" />
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : bookings?.data?.length === 0 ? (
                    <div className="text-center py-6">
                      <Calendar className="mx-auto h-12 w-12 text-muted-foreground" />
                      <h3 className="mt-2 text-sm font-semibold">No bookings yet</h3>
                      <p className="mt-1 text-sm text-muted-foreground">
                        This room hasn't been booked recently.
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {bookings?.data?.slice(0, 10).map((booking: any) => (
                        <div key={booking.id} className="flex items-center space-x-4">
                          <div className="flex-shrink-0">
                            <div className="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center">
                              <Calendar className="h-5 w-5 text-blue-600" />
                            </div>
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium truncate">
                              {booking.title}
                            </p>
                            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                              <Clock className="h-3 w-3" />
                              <span>{formatDateTime(booking.startDate)} - {formatDateTime(booking.endDate)}</span>
                            </div>
                          </div>
                          <Badge className={getStatusColor(booking.status)}>
                            {booking.status}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="availability" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Business Hours</CardTitle>
                </CardHeader>
                <CardContent>
                  {room?.data?.availability?.businessHours ? (
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span>
                        {room.data.availability.businessHours.start} - {room.data.availability.businessHours.end}
                      </span>
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground">No specific business hours set</p>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Working Days</CardTitle>
                </CardHeader>
                <CardContent>
                  {room?.data?.availability?.workingDays ? (
                    <div className="grid grid-cols-7 gap-2">
                      {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day, index) => {
                        const isWorkingDay = room.data.availability.workingDays.includes(index + 1);
                        return (
                          <div
                            key={day}
                            className={`text-center p-2 rounded text-sm ${
                              isWorkingDay 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-gray-100 text-gray-500'
                            }`}
                          >
                            {isWorkingDay ? (
                              <CheckCircle className="h-4 w-4 mx-auto mb-1" />
                            ) : (
                              <XCircle className="h-4 w-4 mx-auto mb-1" />
                            )}
                            {day}
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground">Available all days</p>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Info */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Info</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Capacity: {room?.data?.capacity} people</span>
              </div>
              <div className="flex items-center gap-2">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">
                  Floor {room?.data?.location?.floor}, {room?.data?.location?.room}
                </span>
              </div>
              {room?.data?.availability?.timezone && (
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Timezone: {room.data.availability.timezone}</span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button className="w-full" asChild>
                <Link href={`/dashboard/bookings/new?roomId=${roomId}`}>
                  <Calendar className="mr-2 h-4 w-4" />
                  Book This Room
                </Link>
              </Button>
              <Button variant="outline" className="w-full" asChild>
                <Link href={`/dashboard/rooms/${roomId}/availability`}>
                  <Clock className="mr-2 h-4 w-4" />
                  Check Availability
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

import { MeetingRoomModel, IMeetingRoom, BookingModel } from '../../../models';
import { HttpException } from '../../../exceptions/HttpExceptions';
import { HttpStatus } from '../../../exceptions/HttpStatus';
import { DateHelpers } from '../../../utils/date-helpers';

export class MeetingRoomsService {
  /**
   * Get all meeting rooms with optional filters
   */
  async getAllMeetingRooms(filters: {
    isActive?: boolean;
    capacity?: number;
    floor?: string;
    amenities?: string[];
  } = {}) {
    const query: any = {};
    
    if (filters.isActive !== undefined) {
      query.isActive = filters.isActive;
    }
    
    if (filters.capacity) {
      query.capacity = { $gte: filters.capacity };
    }
    
    if (filters.floor) {
      query['location.floor'] = filters.floor;
    }
    
    if (filters.amenities && filters.amenities.length > 0) {
      query.amenities = { $in: filters.amenities };
    }

    return await MeetingRoomModel.find(query).sort({ name: 1 });
  }

  /**
   * Get meeting room by ID
   */
  async getMeetingRoomById(id: string): Promise<IMeetingRoom> {
    const meetingRoom = await MeetingRoomModel.findById(id);
    
    if (!meetingRoom) {
      throw HttpException.NotFound('Meeting room not found');
    }
    
    return meetingRoom;
  }

  /**
   * Create new meeting room
   */
  async createMeetingRoom(data: Partial<IMeetingRoom>): Promise<IMeetingRoom> {
    try {
      const meetingRoom = new MeetingRoomModel(data);
      return await meetingRoom.save();
    } catch (error: any) {
      if (error.code === 11000) {
        throw HttpException.BadRequest('Meeting room with this name already exists');
      }
      throw HttpException.BadRequest(error.message);
    }
  }

  /**
   * Update meeting room
   */
  async updateMeetingRoom(id: string, data: Partial<IMeetingRoom>): Promise<IMeetingRoom> {
    try {
      const meetingRoom = await MeetingRoomModel.findByIdAndUpdate(
        id,
        { ...data, updatedAt: new Date() },
        { new: true, runValidators: true }
      );
      
      if (!meetingRoom) {
        throw HttpException.NotFound('Meeting room not found');
      }
      
      return meetingRoom;
    } catch (error: any) {
      if (error.code === 11000) {
        throw HttpException.BadRequest('Meeting room with this name already exists');
      }
      throw HttpException.BadRequest(error.message);
    }
  }

  /**
   * Delete meeting room
   */
  async deleteMeetingRoom(id: string): Promise<void> {
    // Check if there are any future bookings
    const futureBookings = await BookingModel.countDocuments({
      resourceType: 'meeting-room',
      resourceId: id,
      startDate: { $gte: new Date() },
      status: { $in: ['confirmed'] }
    });

    if (futureBookings > 0) {
      throw HttpException.BadRequest('Cannot delete meeting room with future bookings');
    }

    const result = await MeetingRoomModel.findByIdAndDelete(id);
    
    if (!result) {
      throw HttpException.NotFound('Meeting room not found');
    }
  }

  /**
   * Check availability for a meeting room
   */
  async checkAvailability(
    roomId: string,
    startDate: Date,
    endDate: Date
  ): Promise<{ available: boolean; conflicts?: any[] }> {
    const meetingRoom = await this.getMeetingRoomById(roomId);
    
    if (!meetingRoom.isActive) {
      return { available: false, conflicts: ['Meeting room is not active'] };
    }

    // Check business hours
    const isWithinBusinessHours = DateHelpers.isWithinBusinessHours(
      startDate,
      endDate,
      meetingRoom.availability.businessHours,
      meetingRoom.availability.workingDays,
      meetingRoom.availability.timezone
    );

    if (!isWithinBusinessHours) {
      return { available: false, conflicts: ['Outside business hours'] };
    }

    // Check booking duration limits
    const durationMinutes = (endDate.getTime() - startDate.getTime()) / (1000 * 60);
    
    if (durationMinutes < meetingRoom.bookingRules.minBookingDuration) {
      return { 
        available: false, 
        conflicts: [`Minimum booking duration is ${meetingRoom.bookingRules.minBookingDuration} minutes`] 
      };
    }
    
    if (durationMinutes > meetingRoom.bookingRules.maxBookingDuration) {
      return { 
        available: false, 
        conflicts: [`Maximum booking duration is ${meetingRoom.bookingRules.maxBookingDuration} minutes`] 
      };
    }

    // Check advance booking rules
    const now = new Date();
    const hoursUntilStart = (startDate.getTime() - now.getTime()) / (1000 * 60 * 60);
    
    if (hoursUntilStart < meetingRoom.bookingRules.minNoticeHours) {
      return { 
        available: false, 
        conflicts: [`Minimum ${meetingRoom.bookingRules.minNoticeHours} hours notice required`] 
      };
    }

    const daysUntilStart = hoursUntilStart / 24;
    if (daysUntilStart > meetingRoom.bookingRules.advanceBookingDays) {
      return { 
        available: false, 
        conflicts: [`Cannot book more than ${meetingRoom.bookingRules.advanceBookingDays} days in advance`] 
      };
    }

    // Check for conflicting bookings
    const conflicts = await BookingModel.find({
      resourceType: 'meeting-room',
      resourceId: roomId,
      status: { $in: ['confirmed'] },
      $or: [
        {
          startDate: { $lt: endDate },
          endDate: { $gt: startDate }
        }
      ]
    }).populate('userId', 'name email');

    if (conflicts.length > 0) {
      return { available: false, conflicts };
    }

    return { available: true };
  }

  /**
   * Get meeting room availability for a date range
   */
  async getAvailabilityCalendar(
    roomId: string,
    startDate: Date,
    endDate: Date
  ): Promise<any[]> {
    const bookings = await BookingModel.find({
      resourceType: 'meeting-room',
      resourceId: roomId,
      status: { $in: ['confirmed'] },
      startDate: { $lt: endDate },
      endDate: { $gt: startDate }
    }).populate('userId', 'name email').sort({ startDate: 1 });

    return bookings.map(booking => ({
      id: booking._id,
      title: booking.title,
      start: booking.startDate,
      end: booking.endDate,
      user: booking.userId,
      status: booking.status
    }));
  }

  /**
   * Get meeting rooms with their current availability status
   */
  async getMeetingRoomsWithAvailability(date?: Date): Promise<any[]> {
    const targetDate = date || new Date();
    const startOfDay = new Date(targetDate);
    startOfDay.setHours(0, 0, 0, 0);
    
    const endOfDay = new Date(targetDate);
    endOfDay.setHours(23, 59, 59, 999);

    const rooms = await MeetingRoomModel.find({ isActive: true }).sort({ name: 1 });
    
    const roomsWithAvailability = await Promise.all(
      rooms.map(async (room) => {
        const bookings = await BookingModel.find({
          resourceType: 'meeting-room',
          resourceId: room._id,
          status: { $in: ['confirmed'] },
          startDate: { $lt: endOfDay },
          endDate: { $gt: startOfDay }
        }).sort({ startDate: 1 });

        const now = new Date();
        let currentStatus = 'available';
        let nextBooking = null;
        let currentBooking = null;

        for (const booking of bookings) {
          if (booking.startDate <= now && booking.endDate > now) {
            currentStatus = 'occupied';
            currentBooking = booking;
            break;
          } else if (booking.startDate > now) {
            nextBooking = booking;
            break;
          }
        }

        return {
          ...room.toJSON(),
          currentStatus,
          currentBooking,
          nextBooking,
          todayBookings: bookings.length
        };
      })
    );

    return roomsWithAvailability;
  }
}

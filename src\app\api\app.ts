import { Elysia } from 'elysia';
import { desksController } from './(app)/modules/desks/desk.controller';
import { meetingRoomController } from './(app)/modules/rooms/meeting-room.controller';
import { bookingController } from './(app)/modules/bookings/booking.controller';
import { guestController } from './(app)/modules/guests/guest.controller';
import { swagger } from '@elysiajs/swagger'


const app = new Elysia({ prefix: '/api' })
  .use(swagger())
  .get('/', () => ({
    message: 'OneCarNow Office Management API',
    version: '1.0.0',
    endpoints: {
      desks: '/api/desks',
      meetingRooms: '/api/meeting-rooms',
      bookings: '/api/bookings',
      guests: '/api/guests'
    }
  }))

  // Health check endpoint
  .get('/health', () => ({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  }))

  // Register all module controllers
  .use(desksController)
  .use(meetingRoomController)
  .use(bookingController)
  .use(guestController)


export type App = typeof app

export default app
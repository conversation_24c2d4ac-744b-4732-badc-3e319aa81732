/* Custom styles for React Big Calendar */

.rbc-calendar {
  font-family: inherit;
}

.rbc-header {
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  padding: 12px 8px;
  font-weight: 600;
  color: #374151;
  text-align: center;
}

.rbc-today {
  background-color: #fef3c7;
}

.rbc-off-range-bg {
  background-color: #f9fafb;
}

.rbc-event {
  border-radius: 6px;
  border: none;
  padding: 2px 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.rbc-event:hover {
  opacity: 0.9;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.rbc-event-label {
  font-size: 11px;
  font-weight: 400;
}

.rbc-slot-selection {
  background-color: rgba(147, 51, 234, 0.2);
  border: 2px solid #9333ea;
  border-radius: 4px;
}

.rbc-time-slot {
  border-top: 1px solid #f1f5f9;
}

.rbc-time-slot:hover {
  background-color: #f8fafc;
}

.rbc-timeslot-group {
  border-bottom: 1px solid #e2e8f0;
}

.rbc-time-gutter .rbc-timeslot-group {
  border-bottom: 1px solid #e2e8f0;
}

.rbc-time-header-gutter {
  background-color: #f8fafc;
}

.rbc-time-header-content {
  border-left: 1px solid #e2e8f0;
}

.rbc-time-content {
  border-top: 1px solid #e2e8f0;
}

.rbc-time-view .rbc-time-gutter {
  background-color: #f8fafc;
  border-right: 1px solid #e2e8f0;
}

.rbc-time-view .rbc-time-gutter .rbc-time-slot {
  color: #6b7280;
  font-size: 12px;
  text-align: right;
  padding-right: 8px;
}

.rbc-day-slot .rbc-time-slot {
  border-top: 1px solid #f1f5f9;
}

.rbc-current-time-indicator {
  background-color: #ef4444;
  height: 2px;
  z-index: 3;
}

.rbc-toolbar {
  margin-bottom: 20px;
  padding: 0 16px;
}

.rbc-toolbar button {
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 8px 16px;
  margin: 0 2px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
}

.rbc-toolbar button:hover {
  background-color: #f3f4f6;
  border-color: #9ca3af;
}

.rbc-toolbar button.rbc-active {
  background-color: #9333ea;
  border-color: #9333ea;
  color: white;
}

.rbc-toolbar button:focus {
  outline: 2px solid #9333ea;
  outline-offset: 2px;
}

.rbc-toolbar .rbc-toolbar-label {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0 16px;
  flex-grow: 1;
  text-align: center;
}

.rbc-btn-group {
  display: flex;
  gap: 4px;
}

.rbc-btn-group button:first-child {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.rbc-btn-group button:last-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.rbc-btn-group button:not(:first-child):not(:last-child) {
  border-radius: 0;
}

/* Week view specific styles */
.rbc-time-view .rbc-header {
  border-bottom: 1px solid #e2e8f0;
}

.rbc-time-view .rbc-allday-cell {
  background-color: #f8fafc;
}

/* Month view specific styles */
.rbc-month-view {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.rbc-month-row {
  border-bottom: 1px solid #e2e8f0;
}

.rbc-month-row:last-child {
  border-bottom: none;
}

.rbc-date-cell {
  border-right: 1px solid #e2e8f0;
  padding: 8px;
  min-height: 100px;
}

.rbc-date-cell:last-child {
  border-right: none;
}

.rbc-date-cell.rbc-off-range {
  background-color: #f9fafb;
  color: #9ca3af;
}

.rbc-date-cell.rbc-today {
  background-color: #fef3c7;
}

.rbc-date-cell > a {
  color: #374151;
  font-weight: 600;
  text-decoration: none;
}

.rbc-date-cell.rbc-today > a {
  color: #92400e;
}

.rbc-show-more {
  background-color: #e5e7eb;
  color: #374151;
  border: none;
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 11px;
  cursor: pointer;
  margin-top: 2px;
}

.rbc-show-more:hover {
  background-color: #d1d5db;
}

/* Custom event colors for different statuses */
.rbc-event.event-confirmed {
  background-color: #3b82f6;
  color: white;
}

.rbc-event.event-completed {
  background-color: #10b981;
  color: white;
}

.rbc-event.event-cancelled {
  background-color: #ef4444;
  color: white;
}

.rbc-event.event-no-show {
  background-color: #6b7280;
  color: white;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .rbc-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .rbc-toolbar .rbc-toolbar-label {
    order: -1;
    margin: 0;
    text-align: center;
  }
  
  .rbc-btn-group {
    justify-content: center;
  }
  
  .rbc-time-view .rbc-time-gutter .rbc-time-slot {
    font-size: 10px;
    padding-right: 4px;
  }
}

import { body, query, param } from 'express-validator';

export class GuestValidator {
  static createGuest = [
    body('name')
      .trim()
      .isLength({ min: 1, max: 100 })
      .withMessage('Name must be between 1 and 100 characters'),
    
    body('email')
      .optional()
      .isEmail()
      .normalizeEmail()
      .withMessage('Must be a valid email address'),
    
    body('phone')
      .optional()
      .matches(/^\+?[\d\s\-\(\)]+$/)
      .withMessage('Phone number format is invalid'),
    
    body('company')
      .optional()
      .trim()
      .isLength({ max: 100 })
      .withMessage('Company name must not exceed 100 characters'),
    
    body('visitDate')
      .isISO8601()
      .withMessage('Visit date must be a valid date')
      .custom((value) => {
        const visitDate = new Date(value);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        if (visitDate < today) {
          throw new Error('Visit date cannot be in the past');
        }
        
        // Check if visit date is not more than 90 days in the future
        const maxDate = new Date();
        maxDate.setDate(maxDate.getDate() + 90);
        if (visitDate > maxDate) {
          throw new Error('Visit date cannot be more than 90 days in the future');
        }
        
        return true;
      }),
    
    body('visitTime.start')
      .matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)
      .withMessage('Start time must be in HH:MM format'),
    
    body('visitTime.end')
      .optional()
      .matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)
      .withMessage('End time must be in HH:MM format')
      .custom((value, { req }) => {
        if (value) {
          const startTime = req.body.visitTime?.start;
          if (startTime) {
            const [startHour, startMin] = startTime.split(':').map(Number);
            const [endHour, endMin] = value.split(':').map(Number);
            
            const startMinutes = startHour * 60 + startMin;
            const endMinutes = endHour * 60 + endMin;
            
            if (endMinutes <= startMinutes) {
              throw new Error('End time must be after start time');
            }
          }
        }
        return true;
      }),
    
    body('purpose')
      .trim()
      .isLength({ min: 1, max: 200 })
      .withMessage('Purpose must be between 1 and 200 characters'),
    
    body('visitInstructions')
      .optional()
      .trim()
      .isLength({ max: 500 })
      .withMessage('Visit instructions must not exceed 500 characters'),
    
    body('identificationRequired')
      .optional()
      .isBoolean()
      .withMessage('Identification required must be a boolean'),
    
    body('accessLevel')
      .optional()
      .isIn(['lobby', 'floor', 'full'])
      .withMessage('Access level must be lobby, floor, or full'),
    
    body('specialRequirements')
      .optional()
      .isArray()
      .withMessage('Special requirements must be an array'),
    
    body('emergencyContact.name')
      .optional()
      .trim()
      .isLength({ max: 100 })
      .withMessage('Emergency contact name must not exceed 100 characters'),
    
    body('emergencyContact.phone')
      .optional()
      .matches(/^\+?[\d\s\-\(\)]+$/)
      .withMessage('Emergency contact phone format is invalid'),
    
    body('emergencyContact.relationship')
      .optional()
      .trim()
      .isLength({ max: 50 })
      .withMessage('Emergency contact relationship must not exceed 50 characters'),
    
    // Custom validation to ensure either email or phone is provided
    body().custom((value) => {
      if (!value.email && !value.phone) {
        throw new Error('Either email or phone number must be provided');
      }
      return true;
    })
  ];

  static updateGuest = [
    param('id')
      .isMongoId()
      .withMessage('Guest ID must be a valid MongoDB ObjectId'),
    
    body('name')
      .optional()
      .trim()
      .isLength({ min: 1, max: 100 })
      .withMessage('Name must be between 1 and 100 characters'),
    
    body('email')
      .optional()
      .isEmail()
      .normalizeEmail()
      .withMessage('Must be a valid email address'),
    
    body('phone')
      .optional()
      .matches(/^\+?[\d\s\-\(\)]+$/)
      .withMessage('Phone number format is invalid'),
    
    body('company')
      .optional()
      .trim()
      .isLength({ max: 100 })
      .withMessage('Company name must not exceed 100 characters'),
    
    body('visitDate')
      .optional()
      .isISO8601()
      .withMessage('Visit date must be a valid date')
      .custom((value) => {
        if (value) {
          const visitDate = new Date(value);
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          
          if (visitDate < today) {
            throw new Error('Visit date cannot be in the past');
          }
        }
        return true;
      }),
    
    body('visitTime.start')
      .optional()
      .matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)
      .withMessage('Start time must be in HH:MM format'),
    
    body('visitTime.end')
      .optional()
      .matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)
      .withMessage('End time must be in HH:MM format'),
    
    body('purpose')
      .optional()
      .trim()
      .isLength({ min: 1, max: 200 })
      .withMessage('Purpose must be between 1 and 200 characters'),
    
    body('visitInstructions')
      .optional()
      .trim()
      .isLength({ max: 500 })
      .withMessage('Visit instructions must not exceed 500 characters'),
    
    body('accessLevel')
      .optional()
      .isIn(['lobby', 'floor', 'full'])
      .withMessage('Access level must be lobby, floor, or full')
  ];

  static getGuests = [
    query('visitDate')
      .optional()
      .isISO8601()
      .withMessage('Visit date must be a valid date'),
    
    query('status')
      .optional()
      .isIn(['invited', 'confirmed', 'checked-in', 'checked-out', 'no-show', 'cancelled'])
      .withMessage('Status must be one of: invited, confirmed, checked-in, checked-out, no-show, cancelled'),
    
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100'),
    
    query('search')
      .optional()
      .trim()
      .isLength({ min: 1 })
      .withMessage('Search term must not be empty')
  ];

  static getGuestById = [
    param('id')
      .isMongoId()
      .withMessage('Guest ID must be a valid MongoDB ObjectId')
  ];

  static checkInGuest = [
    param('id')
      .isMongoId()
      .withMessage('Guest ID must be a valid MongoDB ObjectId'),
    
    body('identificationNumber')
      .optional()
      .trim()
      .isLength({ min: 1, max: 50 })
      .withMessage('Identification number must be between 1 and 50 characters'),
    
    body('notes')
      .optional()
      .trim()
      .isLength({ max: 500 })
      .withMessage('Notes must not exceed 500 characters')
  ];

  static checkOutGuest = [
    param('id')
      .isMongoId()
      .withMessage('Guest ID must be a valid MongoDB ObjectId'),
    
    body('notes')
      .optional()
      .trim()
      .isLength({ max: 500 })
      .withMessage('Notes must not exceed 500 characters')
  ];

  static cancelGuest = [
    param('id')
      .isMongoId()
      .withMessage('Guest ID must be a valid MongoDB ObjectId'),
    
    body('reason')
      .optional()
      .trim()
      .isLength({ max: 500 })
      .withMessage('Cancellation reason must not exceed 500 characters')
  ];

  static sendInvitation = [
    param('id')
      .isMongoId()
      .withMessage('Guest ID must be a valid MongoDB ObjectId'),
    
    body('channels')
      .optional()
      .isArray()
      .withMessage('Channels must be an array')
      .custom((value) => {
        if (value && value.length > 0) {
          const validChannels = ['email', 'whatsapp'];
          const invalidChannels = value.filter((channel: string) => !validChannels.includes(channel));
          if (invalidChannels.length > 0) {
            throw new Error('Invalid notification channels. Valid options: email, whatsapp');
          }
        }
        return true;
      })
  ];

  static bulkInviteGuests = [
    body('guests')
      .isArray({ min: 1 })
      .withMessage('Guests array must contain at least one guest'),
    
    body('guests.*.name')
      .trim()
      .isLength({ min: 1, max: 100 })
      .withMessage('Each guest name must be between 1 and 100 characters'),
    
    body('guests.*.email')
      .optional()
      .isEmail()
      .normalizeEmail()
      .withMessage('Each guest email must be valid'),
    
    body('guests.*.phone')
      .optional()
      .matches(/^\+?[\d\s\-\(\)]+$/)
      .withMessage('Each guest phone number format is invalid'),
    
    body('guests.*.visitDate')
      .isISO8601()
      .withMessage('Each guest visit date must be valid'),
    
    body('guests.*.purpose')
      .trim()
      .isLength({ min: 1, max: 200 })
      .withMessage('Each guest purpose must be between 1 and 200 characters'),
    
    body('commonSettings.accessLevel')
      .optional()
      .isIn(['lobby', 'floor', 'full'])
      .withMessage('Common access level must be lobby, floor, or full'),
    
    body('commonSettings.identificationRequired')
      .optional()
      .isBoolean()
      .withMessage('Common identification required must be a boolean')
  ];
}

import axios from 'axios';
import { NotificationData, NotificationResult } from './notification.service';

export class WhatsAppService {
  private apiUrl: string;
  private accessToken: string;
  private phoneNumberId: string;

  constructor() {
    this.apiUrl = process.env.WHATSAPP_API_URL || 'https://graph.facebook.com/v17.0';
    this.accessToken = process.env.WHATSAPP_ACCESS_TOKEN || '';
    this.phoneNumberId = process.env.WHATSAPP_PHONE_NUMBER_ID || '';
  }

  async sendGuestInvitation(data: NotificationData): Promise<NotificationResult> {
    const { guest, user } = data;
    
    if (!guest || !user || !guest.phone) {
      throw new Error('Missing required data for WhatsApp guest invitation');
    }

    const message = this.formatGuestInvitationMessage(guest, user);
    
    try {
      const response = await this.sendMessage(guest.phone, message);
      
      return {
        success: true,
        messageId: response.data.messages[0].id,
        sentAt: new Date()
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        sentAt: new Date()
      };
    }
  }

  async sendGuestReminder(data: NotificationData): Promise<NotificationResult> {
    const { guest, user } = data;
    
    if (!guest || !user || !guest.phone) {
      throw new Error('Missing required data for WhatsApp guest reminder');
    }

    const message = this.formatGuestReminderMessage(guest, user);
    
    try {
      const response = await this.sendMessage(guest.phone, message);
      
      return {
        success: true,
        messageId: response.data.messages[0].id,
        sentAt: new Date()
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        sentAt: new Date()
      };
    }
  }

  async sendCustomMessage(phone: string, message: string): Promise<NotificationResult> {
    try {
      const response = await this.sendMessage(phone, message);
      
      return {
        success: true,
        messageId: response.data.messages[0].id,
        sentAt: new Date()
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        sentAt: new Date()
      };
    }
  }

  private async sendMessage(phone: string, message: string): Promise<any> {
    // Clean phone number (remove any non-digit characters except +)
    const cleanPhone = phone.replace(/[^\d+]/g, '');
    
    const payload = {
      messaging_product: 'whatsapp',
      to: cleanPhone,
      type: 'text',
      text: {
        body: message
      }
    };

    const response = await axios.post(
      `${this.apiUrl}/${this.phoneNumberId}/messages`,
      payload,
      {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    return response;
  }

  private formatGuestInvitationMessage(guest: any, host: any): string {
    const visitDate = new Date(guest.visitDate).toLocaleDateString();
    const visitTime = guest.visitTime.start + (guest.visitTime.end ? ` - ${guest.visitTime.end}` : '');
    
    return `
🏢 *Office Visit Invitation*

Hello ${guest.name}!

You have been invited to visit our office by ${host.firstName} ${host.lastName}.

📅 *Date:* ${visitDate}
🕐 *Time:* ${visitTime}
🎯 *Purpose:* ${guest.purpose}
👤 *Host:* ${host.firstName} ${host.lastName} (${host.department})

${guest.visitInstructions ? `📝 *Instructions:* ${guest.visitInstructions}\n\n` : ''}

Please bring a valid ID for security purposes.

We look forward to your visit!

*Office Management Team*
    `.trim();
  }

  private formatGuestReminderMessage(guest: any, host: any): string {
    const visitDate = new Date(guest.visitDate).toLocaleDateString();
    const visitTime = guest.visitTime.start + (guest.visitTime.end ? ` - ${guest.visitTime.end}` : '');
    
    return `
⏰ *Visit Reminder*

Hello ${guest.name}!

This is a reminder about your upcoming visit:

📅 *Date:* ${visitDate}
🕐 *Time:* ${visitTime}
🎯 *Purpose:* ${guest.purpose}
👤 *Host:* ${host.firstName} ${host.lastName}

Don't forget to bring your ID!

See you soon!

*Office Management Team*
    `.trim();
  }

  // Method to send template messages (for more complex formatting)
  async sendTemplateMessage(phone: string, templateName: string, parameters: string[]): Promise<NotificationResult> {
    try {
      const cleanPhone = phone.replace(/[^\d+]/g, '');
      
      const payload = {
        messaging_product: 'whatsapp',
        to: cleanPhone,
        type: 'template',
        template: {
          name: templateName,
          language: {
            code: 'en_US'
          },
          components: [
            {
              type: 'body',
              parameters: parameters.map(param => ({
                type: 'text',
                text: param
              }))
            }
          ]
        }
      };

      const response = await axios.post(
        `${this.apiUrl}/${this.phoneNumberId}/messages`,
        payload,
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      return {
        success: true,
        messageId: response.data.messages[0].id,
        sentAt: new Date()
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        sentAt: new Date()
      };
    }
  }

  // Method to send location messages
  async sendLocationMessage(phone: string, latitude: number, longitude: number, name: string, address: string): Promise<NotificationResult> {
    try {
      const cleanPhone = phone.replace(/[^\d+]/g, '');
      
      const payload = {
        messaging_product: 'whatsapp',
        to: cleanPhone,
        type: 'location',
        location: {
          latitude,
          longitude,
          name,
          address
        }
      };

      const response = await axios.post(
        `${this.apiUrl}/${this.phoneNumberId}/messages`,
        payload,
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      return {
        success: true,
        messageId: response.data.messages[0].id,
        sentAt: new Date()
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        sentAt: new Date()
      };
    }
  }

  // Method to check message status
  async getMessageStatus(messageId: string): Promise<any> {
    try {
      const response = await axios.get(
        `${this.apiUrl}/${messageId}`,
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`
          }
        }
      );

      return response.data;
    } catch (error) {
      throw new Error(`Failed to get message status: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

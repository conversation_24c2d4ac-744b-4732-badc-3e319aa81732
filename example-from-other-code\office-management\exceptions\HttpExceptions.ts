export class HttpException extends Error {
  public status: number;
  public message: string;
  public data?: any;

  constructor(status: number, message: string, data?: any) {
    super(message);
    this.status = status;
    this.message = message;
    this.data = data;
  }

  static BadRequest(message: string = 'Bad Request', data?: any): HttpException {
    return new HttpException(400, message, data);
  }

  static Unauthorized(message: string = 'Unauthorized', data?: any): HttpException {
    return new HttpException(401, message, data);
  }

  static Forbidden(message: string = 'Forbidden', data?: any): HttpException {
    return new HttpException(403, message, data);
  }

  static NotFound(message: string = 'Not Found', data?: any): HttpException {
    return new HttpException(404, message, data);
  }

  static Conflict(message: string = 'Conflict', data?: any): HttpException {
    return new HttpException(409, message, data);
  }

  static UnprocessableEntity(message: string = 'Unprocessable Entity', data?: any): HttpException {
    return new HttpException(422, message, data);
  }

  static InternalServerError(message: string = 'Internal Server Error', data?: any): HttpException {
    return new HttpException(500, message, data);
  }

  static ServiceUnavailable(message: string = 'Service Unavailable', data?: any): HttpException {
    return new HttpException(503, message, data);
  }
}

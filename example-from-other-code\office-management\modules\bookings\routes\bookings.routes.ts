import { Router } from 'express';
import {
  createBooking,
  getUserBookings,
  getBookingById,
  updateBooking,
  cancelBooking,
  getAllBookings,
  getBookingStats,
  getTodayBookings
} from '../controllers/bookings.controller';
import { verifyTokenOfficeManagement, verifyAdminRole, verifyReceptionistOrAdmin } from '../../../middlewares/verification-token';

const bookingsRouter = Router();

// User routes (authenticated users)
bookingsRouter.post('/bookings', verifyTokenOfficeManagement, createBooking);
bookingsRouter.get('/bookings/my', verifyTokenOfficeManagement, getUserBookings);
bookingsRouter.get('/bookings/today', verifyTokenOfficeManagement, verifyReceptionistOrAdmin, getTodayBookings);
bookingsRouter.get('/bookings/stats', verifyTokenOfficeManagement, getBookingStats);
bookingsRouter.get('/bookings/:id', verifyTokenOfficeManagement, getBookingById);
bookingsRouter.put('/bookings/:id', verifyTokenOfficeManagement, updateBooking);
bookingsRouter.patch('/bookings/:id/cancel', verifyTokenOfficeManagement, cancelBooking);

// Admin only routes
bookingsRouter.get('/bookings', verifyTokenOfficeManagement, verifyAdminRole, getAllBookings);

export default bookingsRouter;

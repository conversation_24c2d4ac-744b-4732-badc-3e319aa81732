import { verifyToken } from '@/middlewares/verifyToken';
import { MiddlewareController } from '@/types&interfaces/types';
import { GOOGLE_SSO_CONFIG } from '../constants';

export const verifyTokenOfficeManagement: MiddlewareController = async (req, res, next) => {
  // First use the main verifyToken middleware
  verifyToken(req, res, (error?: any) => {
    if (error) {
      return res.status(401).send({
        message: 'Authentication failed',
        data: 'office-management'
      });
    }

    // Additional validation for office-management
    const user = req.userReq || req.userId;

    if (!user) {
      return res.status(401).send({
        message: 'User information not found in token',
        data: 'office-management'
      });
    }

    // For office management, we'll validate the email domain if available
    // If no email in token, we'll fetch from database or allow based on existing auth
    const userEmail = user.email;
    if (userEmail && !userEmail.endsWith(`@${GOOGLE_SSO_CONFIG.ALLOWED_DOMAIN}`)) {
      return res.status(403).send({
        message: 'Only @onecarnow email addresses are allowed',
        data: 'office-management'
      });
    }

    // Set office management specific user data
    req.userOfficeManagement = {
      userId: user.userId || user._id || user.id,
      email: userEmail,
      role: user.role || 'employee', // Default role
      isAuthenticated: true
    };

    next();
  });
};

export const verifyAdminRole: MiddlewareController = async (req, res, next) => {
  if (!req.userOfficeManagement) {
    return res.status(401).send({
      message: 'Authentication required',
      data: 'office-management'
    });
  }

  if (req.userOfficeManagement.role !== 'admin') {
    return res.status(403).send({
      message: 'Admin role required',
      data: 'office-management'
    });
  }

  next();
};

export const verifyReceptionistOrAdmin: MiddlewareController = async (req, res, next) => {
  if (!req.userOfficeManagement) {
    return res.status(401).send({
      message: 'Authentication required',
      data: 'office-management'
    });
  }

  const allowedRoles = ['admin', 'receptionist'];
  if (!allowedRoles.includes(req.userOfficeManagement.role)) {
    return res.status(403).send({
      message: 'Admin or receptionist role required',
      data: 'office-management'
    });
  }

  next();
};

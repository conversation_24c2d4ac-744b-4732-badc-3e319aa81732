'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  ArrowLeft,
  Calendar,
  Clock,
  MapPin,
  Users,
  Building,
  Plus,
  X,
  CheckCircle
} from 'lucide-react';
import Link from 'next/link';
import { Calendar as BigCalendar, luxonLocalizer } from 'react-big-calendar';
import { DateTime } from 'luxon';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import { useMeetingRooms } from '@/lib/hooks/use-meeting-rooms';
import { useDesks } from '@/lib/hooks/use-desks';
import { useCreateBooking, CreateBookingInput } from '@/lib/hooks/use-bookings';
import { DateTime } from 'luxon';

const localizer = luxonLocalizer(DateTime);

interface FormData {
  resourceType: 'MEETING_ROOM' | 'DESK';
  resourceId: string;
  title: string;
  description?: string;
  startDate: string;
  endDate: string;
  attendees?: {
    internal?: string[];
    external?: string[];
  };
}

export default function NewBookingPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const createBooking = useCreateBooking();
  
  const [selectedResource, setSelectedResource] = useState<any>(null);
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<{ start: Date; end: Date } | null>(null);
  const [internalAttendees, setInternalAttendees] = useState<string[]>([]);
  const [externalAttendees, setExternalAttendees] = useState<string[]>([]);
  const [newInternalAttendee, setNewInternalAttendee] = useState('');
  const [newExternalAttendee, setNewExternalAttendee] = useState('');

  // Get pre-selected room from URL params
  const preSelectedRoomId = searchParams.get('roomId');
  const preSelectedDeskId = searchParams.get('deskId');

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setValue,
    watch,
    reset
  } = useForm<FormData>({
    defaultValues: {
      resourceType: preSelectedRoomId ? 'MEETING_ROOM' : preSelectedDeskId ? 'DESK' : 'MEETING_ROOM',
      resourceId: preSelectedRoomId || preSelectedDeskId || '',
      attendees: {
        internal: [],
        external: []
      }
    }
  });

  const resourceType = watch('resourceType');
  const resourceId = watch('resourceId');

  // Fetch resources based on type
  const { data: meetingRooms, isLoading: loadingRooms } = useMeetingRooms({
    limit: 100,
    isActive: true
  });

  const { data: desks, isLoading: loadingDesks } = useDesks({
    limit: 100,
    isActive: true
  });

  // Set pre-selected resource
  useEffect(() => {
    if (preSelectedRoomId && meetingRooms?.data) {
      const room = meetingRooms.data.find(r => r.id === preSelectedRoomId);
      if (room) {
        setSelectedResource(room);
        setValue('resourceId', preSelectedRoomId);
        setValue('resourceType', 'MEETING_ROOM');
      }
    } else if (preSelectedDeskId && desks?.data) {
      const desk = desks.data.find(d => d.id === preSelectedDeskId);
      if (desk) {
        setSelectedResource(desk);
        setValue('resourceId', preSelectedDeskId);
        setValue('resourceType', 'DESK');
      }
    }
  }, [preSelectedRoomId, preSelectedDeskId, meetingRooms, desks, setValue]);

  // Update selected resource when resourceId changes
  useEffect(() => {
    if (resourceId) {
      const resources = resourceType === 'MEETING_ROOM' ? meetingRooms?.data : desks?.data;
      const resource = resources?.find(r => r.id === resourceId);
      setSelectedResource(resource || null);
    }
  }, [resourceId, resourceType, meetingRooms, desks]);

  const addInternalAttendee = () => {
    if (newInternalAttendee.trim() && !internalAttendees.includes(newInternalAttendee.trim())) {
      const updated = [...internalAttendees, newInternalAttendee.trim()];
      setInternalAttendees(updated);
      setValue('attendees.internal', updated);
      setNewInternalAttendee('');
    }
  };

  const addExternalAttendee = () => {
    if (newExternalAttendee.trim() && !externalAttendees.includes(newExternalAttendee.trim())) {
      const updated = [...externalAttendees, newExternalAttendee.trim()];
      setExternalAttendees(updated);
      setValue('attendees.external', updated);
      setNewExternalAttendee('');
    }
  };

  const removeInternalAttendee = (attendee: string) => {
    const updated = internalAttendees.filter(a => a !== attendee);
    setInternalAttendees(updated);
    setValue('attendees.internal', updated);
  };

  const removeExternalAttendee = (attendee: string) => {
    const updated = externalAttendees.filter(a => a !== attendee);
    setExternalAttendees(updated);
    setValue('attendees.external', updated);
  };

  const handleSelectSlot = ({ start, end }: { start: Date; end: Date }) => {
    setSelectedTimeSlot({ start, end });
    setValue('startDate', start.toISOString());
    setValue('endDate', end.toISOString());
  };

  const onSubmit = async (data: FormData) => {
    try {
      if (!selectedTimeSlot) {
        throw new Error('Por favor selecciona un horario');
      }

      const bookingData: CreateBookingInput = {
        ...data,
        startDate: selectedTimeSlot.start.toISOString(),
        endDate: selectedTimeSlot.end.toISOString(),
        attendees: {
          internal: internalAttendees,
          external: externalAttendees
        }
      };

      await createBooking.mutateAsync(bookingData);
      router.push('/dashboard/bookings');
    } catch (error: any) {
      console.error('Error creating booking:', error);
    }
  };

  const getResourceOptions = () => {
    if (resourceType === 'MEETING_ROOM') {
      return meetingRooms?.data || [];
    }
    return desks?.data || [];
  };

  const formatResourceLocation = (resource: any) => {
    if (!resource?.location) return '';
    const loc = resource.location;
    return `${loc.floor ? `Piso ${loc.floor}` : ''}${loc.room || loc.zone ? ` - ${loc.room || loc.zone}` : ''}`;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
      <div className="max-w-7xl mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild className="rounded-full border-gray-300 text-gray-700 hover:bg-gray-50">
            <Link href="/dashboard/bookings">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Nueva Reserva</h1>
            <p className="text-gray-600">Reserva una sala de reunión o escritorio</p>
          </div>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-3">
            {/* Left Column - Form */}
            <div className="lg:col-span-1 space-y-6">
              {/* Resource Selection */}
              <Card className="border-0 shadow-lg rounded-2xl bg-white/80 backdrop-blur-sm">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-2 text-purple-900">
                    <Building className="h-5 w-5" />
                    Seleccionar Recurso
                  </CardTitle>
                  <CardDescription>
                    Elige el tipo de espacio que necesitas
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label className="text-gray-700 font-medium">Tipo de Recurso</Label>
                    <Select 
                      value={resourceType} 
                      onValueChange={(value: 'MEETING_ROOM' | 'DESK') => {
                        setValue('resourceType', value);
                        setValue('resourceId', '');
                        setSelectedResource(null);
                      }}
                    >
                      <SelectTrigger className="rounded-xl border-gray-200 focus:border-purple-500">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="MEETING_ROOM">Sala de Reunión</SelectItem>
                        <SelectItem value="DESK">Escritorio</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-gray-700 font-medium">
                      {resourceType === 'MEETING_ROOM' ? 'Sala de Reunión' : 'Escritorio'} *
                    </Label>
                    <Select 
                      value={resourceId} 
                      onValueChange={(value) => setValue('resourceId', value)}
                    >
                      <SelectTrigger className="rounded-xl border-gray-200 focus:border-purple-500">
                        <SelectValue placeholder={`Selecciona ${resourceType === 'MEETING_ROOM' ? 'una sala' : 'un escritorio'}`} />
                      </SelectTrigger>
                      <SelectContent>
                        {getResourceOptions().map((resource) => (
                          <SelectItem key={resource.id} value={resource.id}>
                            <div className="flex flex-col">
                              <span className="font-medium">{resource.name}</span>
                              <span className="text-xs text-gray-500">
                                {formatResourceLocation(resource)}
                                {resourceType === 'MEETING_ROOM' && resource.capacity && ` • ${resource.capacity} personas`}
                              </span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.resourceId && (
                      <p className="text-sm text-red-500">Este campo es requerido</p>
                    )}
                  </div>

                  {/* Selected Resource Info */}
                  {selectedResource && (
                    <div className="p-4 bg-purple-50 rounded-xl border border-purple-200">
                      <div className="flex items-start gap-3">
                        {resourceType === 'MEETING_ROOM' ? (
                          <Building className="h-5 w-5 text-purple-600 mt-0.5" />
                        ) : (
                          <Users className="h-5 w-5 text-purple-600 mt-0.5" />
                        )}
                        <div className="flex-1">
                          <h4 className="font-medium text-purple-900">{selectedResource.name}</h4>
                          <p className="text-sm text-purple-700">
                            {formatResourceLocation(selectedResource)}
                          </p>
                          {resourceType === 'MEETING_ROOM' && selectedResource.capacity && (
                            <p className="text-sm text-purple-700">
                              Capacidad: {selectedResource.capacity} personas
                            </p>
                          )}
                          {selectedResource.amenities && selectedResource.amenities.length > 0 && (
                            <div className="mt-2 flex flex-wrap gap-1">
                              {selectedResource.amenities.slice(0, 3).map((amenity: string) => (
                                <Badge key={amenity} variant="secondary" className="text-xs bg-purple-100 text-purple-700">
                                  {amenity}
                                </Badge>
                              ))}
                              {selectedResource.amenities.length > 3 && (
                                <Badge variant="secondary" className="text-xs bg-purple-100 text-purple-700">
                                  +{selectedResource.amenities.length - 3} más
                                </Badge>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Booking Details */}
              <Card className="border-0 shadow-lg rounded-2xl bg-white/80 backdrop-blur-sm">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-2 text-purple-900">
                    <Calendar className="h-5 w-5" />
                    Detalles de la Reserva
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="title" className="text-gray-700 font-medium">Título de la Reserva *</Label>
                    <Input
                      id="title"
                      {...register('title', { required: 'El título es requerido' })}
                      placeholder="ej., Reunión de Equipo"
                      className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                    />
                    {errors.title && (
                      <p className="text-sm text-red-500">{errors.title.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description" className="text-gray-700 font-medium">Descripción</Label>
                    <Textarea
                      id="description"
                      {...register('description')}
                      placeholder="Descripción opcional de la reunión..."
                      className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500 min-h-[80px]"
                    />
                  </div>

                  {/* Selected Time Slot */}
                  {selectedTimeSlot && (
                    <div className="p-4 bg-green-50 rounded-xl border border-green-200">
                      <div className="flex items-center gap-2 text-green-800">
                        <CheckCircle className="h-5 w-5" />
                        <div>
                          <p className="font-medium">Horario Seleccionado</p>
                          <p className="text-sm">
                            {DateTime.fromJSDate(selectedTimeSlot.start).toFormat('dd/MM/yyyy HH:mm')} - 
                            {DateTime.fromJSDate(selectedTimeSlot.end).toFormat('HH:mm')}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Attendees */}
              <Card className="border-0 shadow-lg rounded-2xl bg-white/80 backdrop-blur-sm">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-2 text-purple-900">
                    <Users className="h-5 w-5" />
                    Asistentes
                  </CardTitle>
                  <CardDescription>
                    Agrega personas que asistirán a la reunión
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Tabs defaultValue="internal" className="space-y-4">
                    <TabsList className="grid w-full grid-cols-2 rounded-xl bg-gray-100">
                      <TabsTrigger value="internal" className="rounded-lg">Internos</TabsTrigger>
                      <TabsTrigger value="external" className="rounded-lg">Externos</TabsTrigger>
                    </TabsList>

                    <TabsContent value="internal" className="space-y-3">
                      <div className="flex gap-2">
                        <Input
                          value={newInternalAttendee}
                          onChange={(e) => setNewInternalAttendee(e.target.value)}
                          placeholder="Email del empleado..."
                          className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                          onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addInternalAttendee())}
                        />
                        <Button
                          type="button"
                          onClick={addInternalAttendee}
                          variant="outline"
                          className="rounded-xl border-purple-200 text-purple-600 hover:bg-purple-50"
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                      {internalAttendees.length > 0 && (
                        <div className="flex flex-wrap gap-2">
                          {internalAttendees.map((attendee) => (
                            <Badge
                              key={attendee}
                              variant="secondary"
                              className="bg-blue-100 text-blue-700 hover:bg-blue-200 rounded-full px-3 py-1"
                            >
                              {attendee}
                              <button
                                type="button"
                                onClick={() => removeInternalAttendee(attendee)}
                                className="ml-2 hover:text-blue-900"
                              >
                                <X className="h-3 w-3" />
                              </button>
                            </Badge>
                          ))}
                        </div>
                      )}
                    </TabsContent>

                    <TabsContent value="external" className="space-y-3">
                      <div className="flex gap-2">
                        <Input
                          value={newExternalAttendee}
                          onChange={(e) => setNewExternalAttendee(e.target.value)}
                          placeholder="Email del invitado externo..."
                          className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                          onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addExternalAttendee())}
                        />
                        <Button
                          type="button"
                          onClick={addExternalAttendee}
                          variant="outline"
                          className="rounded-xl border-purple-200 text-purple-600 hover:bg-purple-50"
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                      {externalAttendees.length > 0 && (
                        <div className="flex flex-wrap gap-2">
                          {externalAttendees.map((attendee) => (
                            <Badge
                              key={attendee}
                              variant="secondary"
                              className="bg-green-100 text-green-700 hover:bg-green-200 rounded-full px-3 py-1"
                            >
                              {attendee}
                              <button
                                type="button"
                                onClick={() => removeExternalAttendee(attendee)}
                                className="ml-2 hover:text-green-900"
                              >
                                <X className="h-3 w-3" />
                              </button>
                            </Badge>
                          ))}
                        </div>
                      )}
                    </TabsContent>
                  </Tabs>
                </CardContent>
              </Card>
            </div>

            {/* Right Column - Calendar */}
            <div className="lg:col-span-2">
              <Card className="border-0 shadow-lg rounded-2xl bg-white/80 backdrop-blur-sm">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-2 text-purple-900">
                    <Clock className="h-5 w-5" />
                    Seleccionar Horario
                  </CardTitle>
                  <CardDescription>
                    Haz clic y arrastra en el calendario para seleccionar el horario
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-96 rounded-xl overflow-hidden border border-gray-200">
                    <BigCalendar
                      localizer={localizer}
                      events={[]}
                      startAccessor="start"
                      endAccessor="end"
                      style={{ height: '100%' }}
                      selectable
                      onSelectSlot={handleSelectSlot}
                      views={['week', 'day']}
                      defaultView="week"
                      step={15}
                      timeslots={4}
                      min={new Date(2024, 0, 1, 8, 0)}
                      max={new Date(2024, 0, 1, 20, 0)}
                      formats={{
                        timeGutterFormat: 'HH:mm',
                        eventTimeRangeFormat: ({ start, end }) =>
                          `${DateTime.fromJSDate(start).toFormat('HH:mm')} - ${DateTime.fromJSDate(end).toFormat('HH:mm')}`
                      }}
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Actions */}
          <div className="flex gap-4 justify-end">
            <Button
              type="button"
              variant="outline"
              asChild
              className="rounded-xl border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              <Link href="/dashboard/bookings">Cancelar</Link>
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || !selectedTimeSlot || !resourceId}
              className="rounded-xl bg-purple-600 hover:bg-purple-700 text-white px-8"
            >
              {isSubmitting ? 'Creando...' : 'Crear Reserva'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}

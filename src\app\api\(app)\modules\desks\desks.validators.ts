
import { t } from 'elysia';

export const createDesk = t.Object({
  name: t.String(),
  location: t.Object({
    building: t.Optional(t.String()),
    floor: t.String(),
    zone: t.String(),
    address: t.Optional(t.String())
  }),
  description: t.Optional(t.String()),
  isActive: t.Optional(t.Bo<PERSON>an()),
  equipment: t.Optional(t.Array(t.String())),
  images: t.Optional(t.Array(t.String()))
});

export const updateDesk = t.Object({
  name: t.Optional(t.String()),
  location: t.Optional(t.Object({
    building: t.Optional(t.String()),
    floor: t.String(),
    zone: t.String(),
    address: t.Optional(t.String())
  })),
  description: t.Optional(t.String()),
  isActive: t.Optional(t.Boolean()),
  equipment: t.Optional(t.Array(t.String())),
  images: t.Optional(t.Array(t.String()))
});

export const getDeskQuery = t.Object({
  page: t.Optional(t.Numeric()),
  limit: t.Optional(t.Numeric()),
  search: t.Optional(t.String()),
  isActive: t.Optional(t.Boolean()),
  floor: t.Optional(t.String()),
  zone: t.Optional(t.String())
});

export const deskParams = t.Object({
  id: t.String()
});

export const checkAvailabilityQuery = t.Object({
  date: t.String(),
  userId: t.Optional(t.String())
});

export type CreateDesk = typeof createDesk.static;
export type UpdateDesk = typeof updateDesk.static;
export type GetDeskQuery = typeof getDeskQuery.static;
export type DeskParams = typeof deskParams.static;
export type CheckAvailabilityQuery = typeof checkAvailabilityQuery.static;
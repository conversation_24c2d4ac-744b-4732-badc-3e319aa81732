import { Router } from 'express';
import { UserController } from '../controllers/user.controller';
import { UserValidator } from '../validators/user.validator';
import { verifyToken } from '../../middlewares/verifyToken';

const router = Router();
const userController = new UserController();

// Apply authentication middleware to all routes
router.use(verifyToken);

// Profile routes (for current user)
router.get('/profile', 
  userController.getProfile.bind(userController)
);

router.put('/profile', 
  UserValidator.updateProfile,
  userController.updateProfile.bind(userController)
);

// Admin routes for user management
router.get('/', 
  UserValidator.getUsers,
  userController.getUsers.bind(userController)
);

router.post('/', 
  UserValidator.createUser,
  userController.createUser.bind(userController)
);

router.get('/search', 
  UserValidator.searchUsers,
  userController.searchUsers.bind(userController)
);

router.get('/departments', 
  userController.getDepartments.bind(userController)
);

router.get('/positions', 
  userController.getPositions.bind(userController)
);

router.get('/:id', 
  UserValidator.getUserById,
  userController.getUserById.bind(userController)
);

router.put('/:id', 
  UserValidator.updateUser,
  userController.updateUser.bind(userController)
);

router.patch('/:id/deactivate', 
  UserValidator.getUserById,
  userController.deactivateUser.bind(userController)
);

router.patch('/:id/activate', 
  UserValidator.getUserById,
  userController.activateUser.bind(userController)
);

router.get('/:id/team', 
  UserValidator.getUserById,
  userController.getTeamMembers.bind(userController)
);

router.get('/:id/reports', 
  UserValidator.getUserById,
  userController.getDirectReports.bind(userController)
);

router.get('/:id/bookings', 
  UserValidator.getUserBookings,
  userController.getUserBookings.bind(userController)
);

router.get('/:id/guests', 
  UserValidator.getUserGuests,
  userController.getUserGuests.bind(userController)
);

router.delete('/:id', 
  UserValidator.getUserById,
  userController.deleteUser.bind(userController)
);

export default router;

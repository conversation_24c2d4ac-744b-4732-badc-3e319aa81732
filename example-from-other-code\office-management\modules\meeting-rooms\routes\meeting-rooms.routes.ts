import { Router } from 'express';
import {
  getAllMeetingRooms,
  getMeetingRoomById,
  createMeetingRoom,
  updateMeetingRoom,
  deleteMeetingRoom,
  checkAvailability,
  getAvailabilityCalendar,
  getMeetingRoomsWithAvailability
} from '../controllers/meeting-rooms.controller';
import { verifyTokenOfficeManagement, verifyAdminRole } from '../../../middlewares/verification-token';

const meetingRoomsRouter = Router();

// Public routes (authenticated users)
meetingRoomsRouter.get('/meeting-rooms', verifyTokenOfficeManagement, getAllMeetingRooms);
meetingRoomsRouter.get('/meeting-rooms/availability', verifyTokenOfficeManagement, getMeetingRoomsWithAvailability);
meetingRoomsRouter.get('/meeting-rooms/:id', verifyTokenOfficeManagement, getMeetingRoomById);
meetingRoomsRouter.get('/meeting-rooms/:id/calendar', verifyTokenOfficeManagement, getAvailabilityCalendar);
meetingRoomsRouter.post('/meeting-rooms/:id/check-availability', verifyTokenOfficeManagement, checkAvailability);

// Admin only routes
meetingRoomsRouter.post('/meeting-rooms', verifyTokenOfficeManagement, verifyAdminRole, createMeetingRoom);
meetingRoomsRouter.put('/meeting-rooms/:id', verifyTokenOfficeManagement, verifyAdminRole, updateMeetingRoom);
meetingRoomsRouter.delete('/meeting-rooms/:id', verifyTokenOfficeManagement, verifyAdminRole, deleteMeetingRoom);

export default meetingRoomsRouter;

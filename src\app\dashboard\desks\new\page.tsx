'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  ArrowLeft,
  Laptop,
  MapPin,
  Clock,
  Plus,
  X,
  Wifi,
  Monitor,
  Coffee,
  Mic,
  Building
} from 'lucide-react';
import Link from 'next/link';
import { useCreateDesk, CreateDeskInput } from '@/lib/hooks/use-desks';

interface FormData extends CreateDeskInput { }

const commonAmenities = [
  { name: 'WiFi', icon: Wifi },
  { name: 'Monitor', icon: Monitor },
  { name: 'Dual Monitor', icon: Monitor },
  { name: 'Keyboard', icon: Building },
  { name: 'Mouse', icon: Building },
  { name: 'Webcam', icon: Mic },
  { name: 'Headset', icon: Mic },
  { name: 'Coffee Nearby', icon: Coffee },
];

const commonEquipment = [
  'Monitor',
  'Dual Monitor',
  'Keyboard',
  'Mouse',
  'Webcam',
  'Headset',
  'Laptop Stand',
  'USB Hub',
];

const deskTypes = [
  { value: 'standard', label: 'Escritorio Estándar' },
  { value: 'standing', label: 'Escritorio de Pie' },
  { value: 'adjustable', label: 'Escritorio Ajustable' },
  { value: 'corner', label: 'Escritorio Esquinero' },
  { value: 'collaborative', label: 'Espacio Colaborativo' },
];

export default function NewDeskPage() {
  const router = useRouter();
  const createDesk = useCreateDesk();
  const [selectedAmenities, setSelectedAmenities] = useState<string[]>([]);
  const [selectedEquipment, setSelectedEquipment] = useState<string[]>([]);
  const [customAmenity, setCustomAmenity] = useState('');
  const [customEquipment, setCustomEquipment] = useState('');

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setValue,
    watch
  } = useForm<FormData>({
    defaultValues: {
      isActive: true,
      type: 'standard',
      bookingRules: {
        minBookingDuration: 60,
        maxBookingDuration: 480,
        advanceBookingDays: 30,
        minNoticeHours: 1
      },
      availability: {
        businessHours: {
          start: '09:00',
          end: '18:00'
        },
        workingDays: [1, 2, 3, 4, 5],
        timezone: 'America/Mexico_City'
      }
    }
  });

  const toggleAmenity = (amenity: string) => {
    setSelectedAmenities(prev =>
      prev.includes(amenity)
        ? prev.filter(a => a !== amenity)
        : [...prev, amenity]
    );
  };

  const toggleEquipment = (equipment: string) => {
    setSelectedEquipment(prev =>
      prev.includes(equipment)
        ? prev.filter(e => e !== equipment)
        : [...prev, equipment]
    );
  };

  const addCustomAmenity = () => {
    if (customAmenity.trim() && !selectedAmenities.includes(customAmenity.trim())) {
      setSelectedAmenities(prev => [...prev, customAmenity.trim()]);
      setCustomAmenity('');
    }
  };

  const addCustomEquipment = () => {
    if (customEquipment.trim() && !selectedEquipment.includes(customEquipment.trim())) {
      setSelectedEquipment(prev => [...prev, customEquipment.trim()]);
      setCustomEquipment('');
    }
  };

  const onSubmit = async (data: FormData) => {
    try {
      const formData = {
        ...data,
        amenities: selectedAmenities,
        equipment: selectedEquipment,
      };

      await createDesk.mutateAsync(formData);
      router.push('/dashboard/desks');
    } catch (error) {
      console.error('Error creating desk:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
      <div className="max-w-4xl mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild className="rounded-full border-gray-300 text-gray-700 hover:bg-gray-50">
            <Link href="/dashboard/desks">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Crear Escritorio</h1>
            <p className="text-gray-600">Agrega un nuevo espacio de trabajo a tu oficina</p>
          </div>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <Card className="border-0 shadow-lg rounded-2xl bg-white/80 backdrop-blur-sm">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2 text-purple-900">
                <Laptop className="h-5 w-5" />
                Información Básica
              </CardTitle>
              <CardDescription>
                Detalles esenciales sobre el escritorio
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="name" className="text-gray-700 font-medium">Nombre del Escritorio *</Label>
                  <Input
                    id="name"
                    {...register('name', { required: 'El nombre del escritorio es requerido' })}
                    placeholder="ej., Escritorio A-01"
                    className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                  />
                  {errors.name && (
                    <p className="text-sm text-red-500">{errors.name.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="type" className="text-gray-700 font-medium">Tipo de Escritorio *</Label>
                  <Select onValueChange={(value) => setValue('type', value as any)}>
                    <SelectTrigger className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500">
                      <SelectValue placeholder="Selecciona el tipo de escritorio" />
                    </SelectTrigger>
                    <SelectContent>
                      {deskTypes.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.type && (
                    <p className="text-sm text-red-500">{errors.type.message}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description" className="text-gray-700 font-medium">Descripción</Label>
                <Textarea
                  id="description"
                  {...register('description')}
                  placeholder="Breve descripción del escritorio..."
                  className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500 min-h-[100px]"
                />
              </div>
            </CardContent>
          </Card>

          {/* Location */}
          <Card className="border-0 shadow-lg rounded-2xl bg-white/80 backdrop-blur-sm">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2 text-purple-900">
                <MapPin className="h-5 w-5" />
                Ubicación
              </CardTitle>
              <CardDescription>
                ¿Dónde está ubicado este escritorio?
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <Label htmlFor="building" className="text-gray-700 font-medium">Edificio</Label>
                  <Input
                    id="building"
                    {...register('location.building')}
                    placeholder="ej., Edificio Principal"
                    className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="floor" className="text-gray-700 font-medium">Piso *</Label>
                  <Input
                    id="floor"
                    {...register('location.floor', { required: 'El piso es requerido' })}
                    placeholder="ej., 3"
                    className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                  />
                  {errors.location?.floor && (
                    <p className="text-sm text-red-500">{errors.location.floor.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="room" className="text-gray-700 font-medium">Área/Zona *</Label>
                  <Input
                    id="room"
                    {...register('location.room', { required: 'El área es requerida' })}
                    placeholder="ej., Zona A"
                    className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                  />
                  {errors.location?.room && (
                    <p className="text-sm text-red-500">{errors.location.room.message}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="address" className="text-gray-700 font-medium">Dirección</Label>
                <Input
                  id="address"
                  {...register('location.address')}
                  placeholder="Dirección completa (opcional)"
                  className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                />
              </div>
            </CardContent>
          </Card>

          {/* Amenities */}
          <Card className="border-0 shadow-lg rounded-2xl bg-white/80 backdrop-blur-sm">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2 text-purple-900">
                <Wifi className="h-5 w-5" />
                Comodidades
              </CardTitle>
              <CardDescription>
                ¿Qué comodidades están disponibles en este escritorio?
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-3 md:grid-cols-4">
                {commonAmenities.map((amenity) => {
                  const Icon = amenity.icon;
                  const isSelected = selectedAmenities.includes(amenity.name);
                  return (
                    <button
                      key={amenity.name}
                      type="button"
                      onClick={() => toggleAmenity(amenity.name)}
                      className={`p-3 rounded-xl border-2 transition-all duration-200 flex items-center gap-2 ${isSelected
                        ? 'border-purple-500 bg-purple-50 text-purple-700'
                        : 'border-gray-200 hover:border-purple-300 text-gray-600'
                        }`}
                    >
                      <Icon className="h-4 w-4" />
                      <span className="text-sm font-medium">{amenity.name}</span>
                    </button>
                  );
                })}
              </div>

              {/* Custom Amenity */}
              <div className="flex gap-2">
                <Input
                  value={customAmenity}
                  onChange={(e) => setCustomAmenity(e.target.value)}
                  placeholder="Agregar comodidad personalizada..."
                  className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addCustomAmenity())}
                />
                <Button
                  type="button"
                  onClick={addCustomAmenity}
                  variant="outline"
                  className="rounded-xl border-purple-200 text-purple-600 hover:bg-purple-50"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>

              {/* Selected Amenities */}
              {selectedAmenities.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {selectedAmenities.map((amenity) => (
                    <Badge
                      key={amenity}
                      variant="secondary"
                      className="bg-purple-100 text-purple-700 hover:bg-purple-200 rounded-full px-3 py-1"
                    >
                      {amenity}
                      <button
                        type="button"
                        onClick={() => toggleAmenity(amenity)}
                        className="ml-2 hover:text-purple-900"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Equipment */}
          <Card className="border-0 shadow-lg rounded-2xl bg-white/80 backdrop-blur-sm">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2 text-purple-900">
                <Monitor className="h-5 w-5" />
                Equipamiento
              </CardTitle>
              <CardDescription>
                ¿Qué equipamiento está disponible en este escritorio?
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-3 md:grid-cols-4">
                {commonEquipment.map((equipment) => {
                  const isSelected = selectedEquipment.includes(equipment);
                  return (
                    <button
                      key={equipment}
                      type="button"
                      onClick={() => toggleEquipment(equipment)}
                      className={`p-3 rounded-xl border-2 transition-all duration-200 text-sm font-medium ${isSelected
                        ? 'border-purple-500 bg-purple-50 text-purple-700'
                        : 'border-gray-200 hover:border-purple-300 text-gray-600'
                        }`}
                    >
                      {equipment}
                    </button>
                  );
                })}
              </div>

              {/* Custom Equipment */}
              <div className="flex gap-2">
                <Input
                  value={customEquipment}
                  onChange={(e) => setCustomEquipment(e.target.value)}
                  placeholder="Agregar equipamiento personalizado..."
                  className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addCustomEquipment())}
                />
                <Button
                  type="button"
                  onClick={addCustomEquipment}
                  variant="outline"
                  className="rounded-xl border-purple-200 text-purple-600 hover:bg-purple-50"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>

              {/* Selected Equipment */}
              {selectedEquipment.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {selectedEquipment.map((equipment) => (
                    <Badge
                      key={equipment}
                      variant="secondary"
                      className="bg-purple-100 text-purple-700 hover:bg-purple-200 rounded-full px-3 py-1"
                    >
                      {equipment}
                      <button
                        type="button"
                        onClick={() => toggleEquipment(equipment)}
                        className="ml-2 hover:text-purple-900"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Booking Rules */}
          <Card className="border-0 shadow-lg rounded-2xl bg-white/80 backdrop-blur-sm">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2 text-purple-900">
                <Clock className="h-5 w-5" />
                Reglas de Reserva
              </CardTitle>
              <CardDescription>
                Establece restricciones de reserva y disponibilidad
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="minDuration" className="text-gray-700 font-medium">Duración Mínima (minutos)</Label>
                  <Input
                    id="minDuration"
                    type="number"
                    {...register('bookingRules.minBookingDuration')}
                    className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxDuration" className="text-gray-700 font-medium">Duración Máxima (minutos)</Label>
                  <Input
                    id="maxDuration"
                    type="number"
                    {...register('bookingRules.maxBookingDuration')}
                    className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="advanceDays" className="text-gray-700 font-medium">Reserva Anticipada (días)</Label>
                  <Input
                    id="advanceDays"
                    type="number"
                    {...register('bookingRules.advanceBookingDays')}
                    className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="minNotice" className="text-gray-700 font-medium">Aviso Mínimo (horas)</Label>
                  <Input
                    id="minNotice"
                    type="number"
                    {...register('bookingRules.minNoticeHours')}
                    className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                  />
                </div>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="startTime" className="text-gray-700 font-medium">Inicio Horario Laboral</Label>
                  <Input
                    id="startTime"
                    type="time"
                    {...register('availability.businessHours.start')}
                    className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="endTime" className="text-gray-700 font-medium">Fin Horario Laboral</Label>
                  <Input
                    id="endTime"
                    type="time"
                    {...register('availability.businessHours.end')}
                    className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="flex gap-4 justify-end">
            <Button
              type="button"
              variant="outline"
              asChild
              className="rounded-xl border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              <Link href="/dashboard/desks">Cancelar</Link>
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="rounded-xl bg-purple-600 hover:bg-purple-700 text-white px-8"
            >
              {isSubmitting ? 'Creando...' : 'Crear Escritorio'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
